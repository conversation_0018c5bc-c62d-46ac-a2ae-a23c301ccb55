import React from "react";
import { useTheme } from "@/hooks/useTheme";
import FormField from "@/components/molecules/FormField";
import { Form } from "./styles";
import { useFormContext } from "react-hook-form";
import { ScrollView, View } from "react-native";
import {
  ButtonContainer,
  CancelButton,
  ConfirmButton,
  ButtonText,
  Buttontext,
} from "./styles";

interface DispatchFormProps {
  loading: boolean;
  t: (key: string) => string;
  onCancel: () => void;
  onSubmit: (data: any) => void;
}

export const DispatchForm: React.FC<DispatchFormProps> = ({
  loading,
  t,
  onCancel,
  onSubmit,
}) => {
  const {
    formState: { isValid },
    handleSubmit,
    trigger,
  } = useFormContext();
  const { theme } = useTheme();

  return (
    <View style={{ flex: 1 }}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        style={{ flex: 1 }}
        contentContainerStyle={{ paddingBottom: 20 }}
      >
        <Form>
          <FormField
            name="vehical_no"
            label={t("order.vehicle_number")}
            placeholder={t("order.enter_vehicle_number")}
            placeholderTextColor={theme.colors.gray}
            rules={{ required: true }}
            editable={!loading}
            autoCapitalize="characters"
          />

          <FormField
            name="courier_name"
            label={t("order.courier_name")}
            placeholder={t("order.enter_courier_name")}
            placeholderTextColor={theme.colors.gray}
            rules={{ required: true }}
            editable={!loading}
          />

          <FormField
            name="tracking"
            label={t("order.tracking_id")}
            placeholder={t("order.enter_tracking_id")}
            placeholderTextColor={theme.colors.gray}
            rules={{ required: true }}
            editable={!loading}
            autoCapitalize="characters"
          />

          <FormField
            name="tracking_url"
            label={t("order.tracking_url")}
            placeholder={t("order.enter_tracking_url")}
            placeholderTextColor={theme.colors.gray}
            editable={!loading}
          />

          <FormField
            name="notes"
            label={t("order.notes")}
            placeholder={t("order.enter_notes")}
            placeholderTextColor={theme.colors.gray}
            multiline
            numberOfLines={2}
            editable={!loading}
          />
        </Form>
      </ScrollView>

      <ButtonContainer>
        <CancelButton onPress={onCancel} disabled={loading} loading={loading}>
          <Buttontext>{t("common.cancel")}</Buttontext>
        </CancelButton>
        <ConfirmButton
          onPress={() => {
            trigger();
            handleSubmit(onSubmit)();
          }}
          disabled={loading}
          loading={loading}
          title={t("order.dispatch")}
        />
      </ButtonContainer>
    </View>
  );
};
