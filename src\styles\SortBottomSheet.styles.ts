import { styled } from "../utils/styled";

export const Container = styled.View`
  padding: 20px;
`;

export const Title = styled.Text`
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 12px;
  color: ${({ theme }) => theme.colors.text};
`;

export const OptionContainer = styled.Pressable`
  padding-vertical: 12px;
  flex-direction: row;
  align-items: center;
`;

export const OptionText = styled.Text`
  color: ${({ theme }) => theme.colors.text};
  margin-left: 8px;
`;
