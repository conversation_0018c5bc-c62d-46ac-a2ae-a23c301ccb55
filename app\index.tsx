import { useEffect } from "react";
import { useRouter } from "expo-router";
import { RootState, useAppSelector } from "@/store/store";
import { LoadingOverlay } from "@/components";
import { useNavigation } from "@react-navigation/native";

export default function IndexScreen() {
  const router = useRouter();
  const navigation = useNavigation();
  const token = useAppSelector((state: RootState) => state.auth.token);

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (!token) {
        router.replace("/(auth)/intro");
      } else {
        router.replace("/(protected)/(tabs)/home");
      }
    }, 0);
    return () => clearTimeout(timeout);
  }, [navigation, token]);

  return <LoadingOverlay isLoading={true} size="large" />;
}
