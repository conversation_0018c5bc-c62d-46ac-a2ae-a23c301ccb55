import { styled } from "@/utils/styled";
import { Text } from "react-native";

interface StyledPriceTextProps {
  variant: 'primary' | 'secondary' | 'bold';
  size: 'small' | 'medium' | 'large';
}

export const StyledPriceText = styled(Text)<StyledPriceTextProps>`
  color: ${({ theme, variant }) => {
    switch (variant) {
      case 'primary':
        return theme.colors.primary;
      case 'secondary':
        return theme.colors.text;
      case 'bold':
        return theme.colors.text;
      default:
        return theme.colors.primary;
    }
  }};
  
  font-size: ${({ size }) => {
    switch (size) {
      case 'small':
        return '12px';
      case 'medium':
        return '16px';
      case 'large':
        return '20px';
      default:
        return '16px';
    }
  }};
  
  font-weight: ${({ variant }) => {
    switch (variant) {
      case 'bold':
        return '700';
      case 'primary':
        return '600';
      case 'secondary':
        return '500';
      default:
        return '600';
    }
  }};
`;
