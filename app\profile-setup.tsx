import React, { useEffect } from "react";
import { useRouter } from "expo-router";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { RootState, useAppDispatch, useAppSelector } from "@/store/store";
import Toast from "react-native-toast-message";
import { updateUser } from "@/store/slices/authSlice";
import { ProfileSetupForm } from "@/components";
import {
  updateUserAction,
  getUserDetailsAction,
  getStateListAction,
  getCityListAction,
} from "@/store/actions/auth";
import {
  addUpdateAddressAction,
  AddAddressPayload,
} from "@/store/actions/address";
import FormTemplate from "@/template/FormTemplate";
import { Container } from "@/styles/ProfileSetup.styles";
const GSTIN_REGEX = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][A-Z0-9]Z[A-Z0-9]$/;

export interface ProfileSetupFormData {
  first_name: string;
  last_name: string;
  email: string;
  address_line_one: string;
  address_line_two: string;
  post_code: string;
  gst_number: string;
  country_id: number;
  state_id: number;
  city_id: number;
}

const schema = yup.object().shape({
  first_name: yup
    .string()
    .required("First name is required")
    .transform((value) => value?.trim())
    .min(2, "First name must be at least 2 characters")
    .max(50, "First name must not exceed 50 characters")
    .matches(/^[a-zA-Z\s]*$/, "First name can only contain letters and spaces"),
  last_name: yup
    .string()
    .required("Last name is required")
    .transform((value) => value?.trim())
    .min(2, "Last name must be at least 2 characters")
    .max(50, "Last name must not exceed 50 characters")
    .matches(/^[a-zA-Z\s]*$/, "Last name can only contain letters and spaces"),
  email: yup
    .string()
    .email("Please enter a valid email address")
    .max(100, "Email must not exceed 100 characters"),
  address_line_one: yup
    .string()
    .min(5, "Address line one must be at least 5 characters")
    .max(100, "Address line one must not exceed 100 characters"),
  post_code: yup
    .string()
    .matches(/^\d{6}$/, "Post code must be exactly 6 digits"),
  address_line_two: yup
    .string()
    .transform((val) => val?.trim() || "")
    .test(
      "len",
      "Address line two must be between 2 and 100 characters",
      (val) => !val || (val.length >= 2 && val.length <= 100)
    ),

  gst_number: yup
    .string()
    .transform((val) => val?.trim() || "")
    .test(
      "gst",
      "Please enter a valid GSTIN number (e.g., 27ABCDE1234F1Z5)",
      (val) => !val || GSTIN_REGEX.test(val)
    ),
  country_id: yup.number().min(1, "Please select a country"),
  state_id: yup.number().min(1, "Please select a state"),
  city_id: yup.number().min(1, "Please select a city"),
});

const ProfileSetupScreen = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { user, stateList } = useAppSelector((state: RootState) => state.auth);
  useEffect(() => {
    if (user?.country_id && !stateList) {
      dispatch(getStateListAction(user?.country_id)).unwrap();
    }
    if (user?.state_id) {
      dispatch(getCityListAction(user?.state_id)).unwrap();
    }
  }, [user?.country_id, stateList]);

  const onSubmit = async (data: ProfileSetupFormData) => {
    try {
      const response = await dispatch(
        updateUserAction({ ...user, ...data })
      ).unwrap();

      if (response.status) {
        const addressPayload: AddAddressPayload = {
          billing_shipping: 1,
          contact_person_name: `${data.first_name} ${data.last_name}`,
          company_name: "",
          address_line_one: data.address_line_one,
          address_line_two: data.address_line_two || "",
          gst_number: data.gst_number || "",
          post_code: data.post_code,
          country_id: data.country_id,
          state_id: data.state_id,
          city_id: data.city_id,
          phone: user?.mobile || "",
        };

        try {
          await dispatch(addUpdateAddressAction(addressPayload)).unwrap();

          await dispatch(getUserDetailsAction({}));
        } catch (addressError) {
          console.warn("Address creation failed:", addressError);
        }

        dispatch(updateUser({ ...data, is_profile_complete: 1 }));

        Toast.show({
          type: "success",
          text1: response.message,
        });

        router.replace("/");
      } else {
        Toast.show({
          type: "error",
          text1: response.message || "Profile update failed",
        });
      }
    } catch (error) {
      Toast.show({
        type: "error",
        text1: error?.message || "An error occurred while updating profile",
      });
    }
  };

  return (
    <Container>
      <FormTemplate<ProfileSetupFormData>
        Component={ProfileSetupForm}
        onSubmit={onSubmit}
        defaultValues={{
          first_name: user?.first_name || "",
          last_name: user?.last_name || "",
          email: user?.email || "",
          address_line_one: user?.address_line_one || "",
          address_line_two: user?.address_line_two || "",
          post_code: user?.post_code || "",
          gst_number: user?.gst_number || (__DEV__ ? "27ABCDE1234F1Z5" : ""),
          country_id: user?.country_id || null,
          state_id: user?.state_id || null,
          city_id: user?.city_id || null,
        }}
        resolver={yupResolver(schema)}
        mode="onChange"
      />
    </Container>
  );
};

export default ProfileSetupScreen;
