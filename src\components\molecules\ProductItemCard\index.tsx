import React from "react";
import { useTranslation } from "react-i18next";
import {
  Container,
  ImageContainer,
  ProductImage,
  InfoContainer,
  ProductName,
  ProductDetails,
  PriceContainer,
  Price,
  Subtotal,
} from "./styles";

interface ProductItemCardProps {
  productName: string;
  productImage?: string;
  quantity: number;
  price: string;
  subtotal?: string;
}

const ProductItemCard: React.FC<ProductItemCardProps> = ({
  productName,
  productImage,
  quantity,
  price,
  subtotal,
}) => {
  const { t } = useTranslation();

  const calculateSubtotal = () => {
    if (subtotal) return subtotal;
    return (parseFloat(price) * quantity).toFixed(2);
  };

  return (
    <Container>
      <ImageContainer>
        {productImage && (
          <ProductImage
            source={{ uri: productImage }}
            contentFit="cover"
          />
        )}
      </ImageContainer>

      <InfoContainer>
        <ProductName numberOfLines={2}>{productName}</ProductName>
        <ProductDetails>
          {t("quantity")}: {quantity}
        </ProductDetails>
        <ProductDetails>
          {t("price")}: ₹{price}
        </ProductDetails>
      </InfoContainer>

      <PriceContainer>
        <Price>₹{calculateSubtotal()}</Price>
        <Subtotal>{quantity} × ₹{price}</Subtotal>
      </PriceContainer>
    </Container>
  );
};

export default ProductItemCard;
