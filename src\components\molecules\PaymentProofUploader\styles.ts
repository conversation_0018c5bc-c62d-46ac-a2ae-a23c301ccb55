import { styled } from "@/utils/styled";
import { View, Text, Pressable, Image } from "react-native";

export const PaymentProofContainer = styled(View)`
  margin-top: 20px;
  margin-bottom: 16px;
`;

export const PaymentProofLabel = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 12px;
`;

export const PaymentProofButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: ${({ theme }) => theme.colors.card};
  border: 2px dashed ${({ theme }) => theme.colors.border};
  border-radius: 16px;
  min-height: 80px;
  transition: all 0.2s ease;
`;

export const PaymentProofButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.primary};
  font-size: 16px;
  font-weight: 500;
  margin-left: 12px;
`;

export const PaymentProofPreview = styled(View)`
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 3;
`;

export const PaymentProofPreviewImage = styled(Image)`
  width: 100%;
  height: 200px;
  border-radius: 16px;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const PaymentProofRemoveButton = styled(Pressable)`
  position: absolute;
  top: 12px;
  right: 12px;
  flex-direction: row;
  align-items: center;
  padding: 10px 14px;
  background-color: ${({ theme }) => theme.colors.error};
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.2;
  shadow-radius: 4px;
  elevation: 4;
`;

export const PaymentProofRemoveButtonText = styled(Text)`
  color: white;
  font-size: 14px;
  font-weight: 600;
`;

export const PaymentProofInfo = styled(View)`
  margin-top: 16px;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 12px;
  border-left: 4px solid ${({ theme }) => theme.colors.primary};
  shadow-color: #000;
  shadow-offset: 0px 1px;
  shadow-opacity: 0.05;
  shadow-radius: 2px;
  elevation: 1;
`;

export const PaymentProofInfoText = styled(Text)`
  font-size: 13px;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 6px;
  line-height: 18px;
`;
