import React, { useState, useRef, useEffect } from "react";
import { TextInput, Pressable } from "react-native";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import Button from "../../atoms/Button";
import Text from "../../atoms/Text";
import { OTP_LENGTH, RESEND_TIMEOUT } from "@/utils/common";
import {
  Container,
  OTPTitle,
  OTPContainer,
  OTPInput,
  ResendRow,
  ResendText,
  ResendLink,
  TimerText,
  BackButton,
  BackButtonText,
} from "./styles";

interface OTPFormProps {
  onSubmit: (otp: string) => void;
  onResend: () => void;
  onBack: () => void;
  loading?: boolean;
}

const OTPForm: React.FC<OTPFormProps> = ({
  onSubmit,
  onResend,
  onBack,
  loading = false,
}) => {
  const { t } = useTranslation();
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [focusedIndex, setFocusedIndex] = useState(0);
  const [countdown, setCountdown] = useState(0);
  const [canResend, setCanResend] = useState(false);
  const inputRefs = useRef<Array<TextInput | null>>([]);
  const autoVerifyTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    startCountdown();

    return () => {
      if (autoVerifyTimeoutRef.current) {
        clearTimeout(autoVerifyTimeoutRef.current);
      }
    };
  }, []);

  const startCountdown = () => {
    setCountdown(RESEND_TIMEOUT);
    setCanResend(false);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          setCanResend(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleOtpChange = (value: string, index: number) => {
    if (autoVerifyTimeoutRef.current) {
      clearTimeout(autoVerifyTimeoutRef.current);
      autoVerifyTimeoutRef.current = null;
    }

    const numericValue = value.replace(/[^0-9]/g, "");

    if (numericValue.length > 1) {
      handlePaste(numericValue, index);
      return;
    }

    // Handle single character input
    const singleChar = numericValue;
    const newOtp = [...otp];
    newOtp[index] = singleChar;

    updateOtpAndCheckCompletion(newOtp, index, singleChar);
  };

  const updateOtpAndCheckCompletion = (
    newOtp: string[],
    currentIndex: number,
    currentValue: string
  ) => {
    setOtp(newOtp);

    if (currentValue && currentIndex < OTP_LENGTH - 1) {
      const nextIndex = currentIndex + 1;
      inputRefs.current[nextIndex]?.focus();
      setFocusedIndex(nextIndex);
    }

    const otpString = newOtp.join("");
    if (otpString.length === OTP_LENGTH) {
      autoVerifyTimeoutRef.current = setTimeout(() => {
        onSubmit(otpString);
      }, 150);
    }
  };

  const handlePaste = (pastedValue: string, startIndex: number) => {
    if (autoVerifyTimeoutRef.current) {
      clearTimeout(autoVerifyTimeoutRef.current);
      autoVerifyTimeoutRef.current = null;
    }

    const numericValue = pastedValue.replace(/[^0-9]/g, "");

    if (numericValue.length === OTP_LENGTH) {
      const newOtp = numericValue.split("").slice(0, OTP_LENGTH);
      setOtp(newOtp);

      const lastIndex = OTP_LENGTH - 1;
      inputRefs.current[lastIndex]?.focus();
      setFocusedIndex(lastIndex);

      autoVerifyTimeoutRef.current = setTimeout(() => {
        onSubmit(numericValue);
      }, 150);
      return;
    }

    const remainingSlots = OTP_LENGTH - startIndex;
    const valueToPaste = numericValue.slice(0, remainingSlots);

    const newOtp = [...otp];

    for (let i = 0; i < valueToPaste.length; i++) {
      if (startIndex + i < OTP_LENGTH) {
        newOtp[startIndex + i] = valueToPaste[i];
      }
    }

    setOtp(newOtp);

    const nextFocusIndex = Math.min(
      startIndex + valueToPaste.length,
      OTP_LENGTH - 1
    );
    inputRefs.current[nextFocusIndex]?.focus();
    setFocusedIndex(nextFocusIndex);

    const otpString = newOtp.join("");
    if (otpString.length === OTP_LENGTH) {
      autoVerifyTimeoutRef.current = setTimeout(() => {
        onSubmit(otpString);
      }, 150);
    }
  };

  const handleKeyPress = (e: any, index: number) => {
    if (e.nativeEvent.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
      setFocusedIndex(index - 1);
    }
  };

  const handleVerifyOTP = () => {
    const otpValue = otp.join("");
    if (otpValue.length !== OTP_LENGTH) {
      return;
    }
    onSubmit(otpValue);
  };

  const handleResend = () => {
    if (canResend) {
      if (autoVerifyTimeoutRef.current) {
        clearTimeout(autoVerifyTimeoutRef.current);
        autoVerifyTimeoutRef.current = null;
      }

      onResend();
      startCountdown();
      setOtp(["", "", "", "", "", ""]);
      inputRefs.current[0]?.focus();
      setFocusedIndex(0);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <Container>
      <OTPTitle>{t("otp_verification")}</OTPTitle>

      <OTPContainer>
        {Array(OTP_LENGTH)
          .fill(0)
          .map((_, index) => (
            <OTPInput
              key={index}
              ref={(ref) => (inputRefs.current[index] = ref)}
              focused={focusedIndex === index}
              value={otp[index]}
              onChangeText={(value) => handleOtpChange(value, index)}
              onKeyPress={(e) => handleKeyPress(e, index)}
              keyboardType="number-pad"
              maxLength={10}
              selectTextOnFocus
              onFocus={() => setFocusedIndex(index)}
            />
          ))}
      </OTPContainer>

      <ResendRow>
        <ResendText>{t("resend_code")}</ResendText>
        {canResend ? (
          <Pressable onPress={handleResend}>
            <ResendLink>{t("resend")}</ResendLink>
          </Pressable>
        ) : (
          <TimerText>{formatTime(countdown)}</TimerText>
        )}
      </ResendRow>

      <Button
        title={t("verify")}
        onPress={handleVerifyOTP}
        loading={loading}
        style={{ width: "100%" }}
        disabled={otp.join("").length !== OTP_LENGTH || loading}
      />

      <BackButton onPress={onBack}>
        <BackButtonText>{t("back")}</BackButtonText>
      </BackButton>
    </Container>
  );
};

export default OTPForm;
