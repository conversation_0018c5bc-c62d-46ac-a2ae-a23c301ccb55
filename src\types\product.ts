import { ApiResponse } from "./api";

export interface ProductType {
  product_id: number;
  title: string;
  sku: string;
  slug: string;
  hsn_code: string;
  sort_description: string;
  description: string | null;
  quantity: number;
  price: string;
  dealer_price: string;
  meta_title: string;
  display_vendor: string;
  meta_description: string;
  gst_rate_id: number;
  main_image: Media;
  gallery: Media[];
  documents: DocumentFile[];
  categories: Category[];
}

export interface Media {
  id: number;
  model_type: string;
  model_id: number;
  uuid: string;
  collection_name: string;
  name: string;
  file_name: string;
  mime_type: string;
  disk: string;
  conversions_disk: string;
  size: number;
  manipulations: any[];
  custom_properties: any[];
  generated_conversions: {
    thumb: boolean;
    preview: boolean;
  };
  responsive_images: any[];
  order_column: number;
  created_at: string;
  updated_at: string;
  url: string;
  thumbnail: string;
  preview: string;
  original_url: string;
  preview_url: string;
}

export interface DocumentFile {
  id: number;
  model_type: string;
  model_id: number;
  uuid: string;
  collection_name: string;
  name: string;
  file_name: string;
  mime_type: string;
  disk: string;
  conversions_disk: string;
  size: number;
  manipulations: any[];
  custom_properties: any[];
  generated_conversions: any[];
  responsive_images: any[];
  order_column: number;
  created_at: string;
  updated_at: string;
  original_url: string;
  preview_url: string;
}

export interface Category {
  id: number;
  title: string;
  description: string | null;
  slug: string | null;
  banner: string;
  meta_title: string;
  meta_description: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  pivot: {
    product_id: number;
    category_id: number;
  };
}

export interface ProductCategoryType {
  id: number;
  title: string;
}

export interface ProductListResponse {
  current_page: number;
  data: ProductType[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  next_page_url: string | null;
  path: string;
  per_page: number;
  prev_page_url: string | null;
  to: number;
  total: number;
}
export interface ProductListPayloadType {
  page?: number;
  search?: string;
  category_id?: number;
  slug?: string;
  order_by?: number;
  sort_order?: number;
}
export interface ProductCategoryResponseType extends ApiResponse {
  data: ProductCategoryType[];
}
