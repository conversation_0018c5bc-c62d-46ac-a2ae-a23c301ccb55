import React from "react";
import { useTranslation } from "react-i18next";
import ConfirmationModal, {
  ConfirmationModalProps,
} from "../ConfirmationModal";

interface LogoutModalProps
  extends Omit<
    ConfirmationModalProps,
    "title" | "message" | "variant" | "icon"
  > {
  title?: string;
  message?: string;
}

const LogoutModal: React.FC<LogoutModalProps> = ({
  title,
  message,
  cancelText,
  confirmText,
  ...props
}) => {
  const { t } = useTranslation();

  return (
    <ConfirmationModal
      {...props}
      title={title || t("logout.title")}
      message={message || t("logout.message")}
      cancelText={cancelText || t("logout.cancel")}
      confirmText={confirmText || t("logout.confirm")}
      variant="danger"
      icon="log-out-outline"
    />
  );
};

export default LogoutModal;
