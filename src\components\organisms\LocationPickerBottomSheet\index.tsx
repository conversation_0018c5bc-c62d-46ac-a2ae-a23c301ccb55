import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { BottomSheetModal, BottomSheetView } from "@gorhom/bottom-sheet";
import { Ionicons } from "@expo/vector-icons";
import LoadingOverlay from "@/components/atoms/LoadingOverlay";
import {
  Title,
  SearchContainer,
  SearchInput,
  ListContainer,
  LocationItem,
  LocationName,
  EmptyContainer,
  EmptyText,
  LoadingContainer,
  StyledBottomSheetView,
} from "./styles";
import { StateList } from "@/types/auth";
import { useTheme } from "@/hooks/useTheme";
import { useTranslation } from "react-i18next";
import { useBottomSheetBackdrop } from "@/utils/commonBackdrop";

interface Props {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (item: StateList) => void;
  data: StateList[];
  title?: string;
  searchPlaceholder?: string;
  isLoading?: boolean;
}

const LocationPickerBottomSheet: React.FC<Props> = ({
  isVisible,
  onClose,
  onSelect,
  data,
  title = "Select Location",
  searchPlaceholder = "Search location",
  isLoading = false,
}) => {
  const sheetRef = useRef<BottomSheetModal>(null);
  const [query, setQuery] = useState("");
  const [filtered, setFiltered] = useState<StateList[]>(data);
  const { theme } = useTheme();
  const { t } = useTranslation();

  useEffect(() => {
    if (isVisible) {
      sheetRef.current?.present();
    }
  }, [isVisible]);

  useEffect(() => {
    if (!query) {
      setFiltered(data);
      return;
    }
    setFiltered(
      data.filter((item) =>
        item.name.toLowerCase().includes(query.toLowerCase())
      )
    );
  }, [query, data]);

  const handleDismiss = useCallback(() => {
    onClose?.();
  }, [onClose]);

  const snapPoints = useMemo(() => ["100%"], []);

  const handleSearch = useCallback((text: string) => {
    setQuery(text);
    // Implement your search logic here
  }, []);

  const renderLocationItem = useCallback(
    ({ item }: { item: StateList }) => (
      <LocationItem
        onPress={() => {
          onSelect(item);
          onClose();
        }}
      >
        <LocationName>{item.name}</LocationName>
      </LocationItem>
    ),
    [onSelect, onClose]
  );

  const renderBackdrop = useBottomSheetBackdrop();

  return (
    <BottomSheetModal
      ref={sheetRef}
      snapPoints={snapPoints}
      onDismiss={handleDismiss}
      style={{ borderTopLeftRadius: 20, borderTopRightRadius: 20 }}
      backdropComponent={renderBackdrop}
      enablePanDownToClose
    >
      <StyledBottomSheetView>
        <Title>{title}</Title>
        <SearchContainer>
          <Ionicons name="search" size={20} color={theme.colors.text} />
          <SearchInput
            placeholder={searchPlaceholder}
            value={query}
            onChangeText={handleSearch}
            placeholderTextColor={theme.colors.text}
          />
        </SearchContainer>

        <ListContainer
          data={filtered}
          keyExtractor={(item) => item.id.toString()}
          ListEmptyComponent={
            isLoading ? (
              <LoadingContainer>
                <LoadingOverlay isLoading={isLoading} size="large" />
              </LoadingContainer>
            ) : (
              <EmptyContainer>
                <EmptyText>{t("location.noLocationsFound")}</EmptyText>
              </EmptyContainer>
            )
          }
          renderItem={renderLocationItem}
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={{ paddingBottom: 20 }}
        />
      </StyledBottomSheetView>
    </BottomSheetModal>
  );
};

export default LocationPickerBottomSheet;
