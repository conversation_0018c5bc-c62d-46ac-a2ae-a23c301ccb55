import { styled } from "@/utils/styled";
import { Ionicons } from "@expo/vector-icons";

export const Container = styled.View`
  flex: 1;
  padding: 20px;
`;

export const Title = styled.Text`
  font-size: 24px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 24px;
`;

export const VendorInputContainer = styled.Pressable`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.darkGray};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
`;

export const VendorInput = styled.View`
  flex: 1;
`;

export const VendorInputText = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const PlaceholderText = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const DropdownIcon = styled(Ionicons)`
  color: ${({ theme }) => theme.colors.text};
  margin-left: 8px;
`;
