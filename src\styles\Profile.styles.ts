import { styled } from "../utils/styled";
import { View, Text, Pressable, TextInput, ScrollView } from "react-native";

export const Container = styled(View)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const Header = styled(View)`
  flex-direction: row;
  align-items: center;
  padding: 16px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.border};
`;

export const BackButton = styled(Pressable)``;

export const HeaderTitle = styled(Text)`
  font-size: 20px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
  margin-left: -16px;
`;

export const Content = styled(View)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const Form = styled(View)``;

export const SectionTitle = styled(Text)`
  font-size: 18px;
  font-weight: 600;
  margin-top: 16px;
  margin-bottom: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const InputGroup = styled(View)`
  margin-bottom: 20px;
`;

export const Label = styled(Text)`
  font-size: 16px;
  margin-bottom: 8px;
  color: ${({ theme }) => theme.colors.text};
`;

export const Input = styled(TextInput)`
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  background-color: ${({ theme }) => theme.colors.card};
`;

export const TextArea = styled(TextInput)`
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  height: 100px;
  text-align-vertical: top;
  color: ${({ theme }) => theme.colors.text};
  background-color: ${({ theme }) => theme.colors.card};
`;

export const SaveButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.primary};
  margin: 16px;
  padding: 16px;
  border-radius: 8px;
  align-items: center;
`;

export const SaveButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 16px;
  font-weight: 600;
`;

export const Section = styled(View)`
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
`;

export const InfoItem = styled(View)`
  flex-direction: row;
  align-items: center;
  padding-vertical: 12px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.border};
`;

export const InfoContent = styled(View)`
  flex: 1;
  margin-left: 12px;
`;

export const InfoLabel = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  margin-bottom: 4px;
`;

export const InfoValue = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const EditButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.primary};
  margin: 16px;
  padding: 16px;
  border-radius: 8px;
  align-items: center;
`;

export const EditButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 16px;
  font-weight: 600;
`;

export const PickerButton = styled(Pressable)`
  padding: 12px;
  background-color: ${({ theme }) => theme.colors.card};
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  margin-top: 4px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

export const PickerText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const DisabledPickerButton = styled(Pressable)`
  padding: 12px;
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 8px;
  margin-top: 4px;
  opacity: 0.7;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
`;
