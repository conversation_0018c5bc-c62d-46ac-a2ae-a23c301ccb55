import { styled } from "@/utils/styled";
import { View, Text, Pressable } from "react-native";

export const ModalOverlay = styled(Pressable)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.overlay};
  justify-content: center;
  align-items: center;
  padding: 20px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
`;

export const ModalContainer = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 20px;
  max-width: 400px;
  width: 100%;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.25;
  shadow-radius: 8px;
  elevation: 8;
  position: relative;
  z-index: 10000;
  align-self: center;
`;

export const ModalHeader = styled(View)`
  padding: 24px 24px 16px 24px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.divider};
`;

export const ModalTitle = styled(Text)`
  font-size: 20px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
`;

export const ModalContent = styled(View)`
  padding: 24px;
`;

export const OptionContainer = styled(View)`
  flex-direction: row;
  justify-content: space-around;
  margin-bottom: 24px;
`;

export const OptionButton = styled(Pressable)`
  align-items: center;
  padding: 20px;
  background-color: ${({ theme }) => theme.colors.primary + "08"};
  border-radius: 16px;
  min-width: 120px;
  border: 2px solid ${({ theme }) => theme.colors.primary + "15"};
`;

export const OptionIcon = styled(View)`
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: ${({ theme }) => theme.colors.primary + "15"};
  justify-content: center;
  align-items: center;
  margin-bottom: 12px;
`;

export const OptionText = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.primary};
`;

export const CancelButton = styled(Pressable)`
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 12px;
  align-items: center;
`;

export const CancelButtonText = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.white};
`;
