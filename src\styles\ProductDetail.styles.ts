import { View, Text, Image, Pressable } from "react-native";
import { styled } from "../utils/styled";
import Button from "../components/atoms/Button";

export const Container = styled(View)`
  flex: 1;
  background-color: ${(props) => props.theme.colors.background};
`;

export const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

export const ErrorContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

export const ErrorText = styled(Text)`
  color: ${(props) => props.theme.colors.error};
  font-size: 16px;
  text-align: center;
`;

export const RetryButton = styled.Pressable`
  padding-horizontal: 24px;
  padding-vertical: 12px;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.primary};
`;

export const RetryButtonText = styled.Text`
  color: ${({ theme }) => theme.colors.white};
  font-size: 16px;
  font-weight: bold;
`;

export const ImageContainer = styled(View)`
  width: 100%;
  height: 300px;
  background-color: ${(props) => props.theme.colors.card};
  justify-content: center;
  align-items: center;
`;

export const ProductImage = styled(Image)`
  width: 100%;
  height: 100%;
`;

export const ImagePagination = styled.View`
  position: absolute;
  bottom: 16px;
  left: 0;
  right: 0;
  flex-direction: row;
  justify-content: center;
  align-items: center;
`;

export const PaginationDot = styled.View<{ active?: boolean }>`
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-horizontal: 4px;
  background-color: ${({ theme }) => theme.colors.primary};
  opacity: ${({ active }) => (active ? 1 : 0.5)};
`;

export const GalleryContainer = styled(View)`
  padding: 16px;
  background-color: ${(props) => props.theme.colors.card};
`;

export const GalleryImage = styled(Image)`
  border-radius: 8px;
  width: 80px;
  height: 80px;
  margin-right: 8px;
  border-width: ${({ selected }) => (selected ? "2px" : "0px")};
  border-color: ${({ theme }) => theme.colors.primary};
`;

export const Content = styled(View)`
  padding: 16px;
`;

export const Header = styled(View)`
  margin-bottom: 16px;
`;

export const Title = styled(Text)`
  font-size: 24px;
  font-weight: bold;
  color: ${(props) => props.theme.colors.text};
`;

export const PriceContainer = styled(View)`
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
`;

export const PriceLabel = styled(Text)`
  font-size: 16px;
  color: ${(props) => props.theme.colors.text};
  margin-right: 8px;
`;

export const PriceValue = styled(Text)`
  font-size: 24px;
  font-weight: bold;
  color: ${(props) => props.theme.colors.primary};
`;

export const CategoryContainer = styled(View)`
  flex-direction: row;
  flex-wrap: wrap;
  margin-bottom: 16px;
`;

export const CategoryChip = styled(View)`
  background-color: ${(props) => props.theme.colors.primary}20;
  padding: 8px 16px;
  border-radius: 20px;
  margin-right: 8px;
  margin-bottom: 8px;
`;

export const CategoryText = styled(Text)`
  color: ${(props) => props.theme.colors.primary};
  font-size: 14px;
`;

export const DescriptionContainer = styled(View)`
  /* margin-bottom: 24px; */
`;

export const DescriptionText = styled(Text)`
  font-size: 16px;
  color: ${(props) => props.theme.colors.text};
  line-height: 24px;
`;

export const Section = styled(View)`
  margin-bottom: 24px;
`;

export const SectionHeader = styled(View)`
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
`;

export const SectionTitle = styled(Text)`
  font-size: 18px;
  font-weight: bold;
  color: ${(props) => props.theme.colors.text};
  margin-left: 8px;
`;

export const SectionContent = styled(View)`
  background-color: ${(props) => props.theme.colors.card};
  border-radius: 8px;
  padding: 16px;
`;

export const SpecItem = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  padding-vertical: 8px;
  border-bottom-width: 1px;
  border-bottom-color: ${(props) => props.theme.colors.border};
`;

export const SpecLabel = styled(Text)`
  font-size: 14px;
  color: ${(props) => props.theme.colors.textSecondary};
`;

export const SpecValue = styled(Text)`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text};
  font-weight: 500;
`;

export const DocumentContainer = styled(View)`
  margin-top: 16px;
`;

export const DocumentButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  background-color: ${(props) => props.theme.colors.card};
  padding: 16px;
  border-radius: 8px;
`;

export const DocumentText = styled(Text)`
  font-size: 16px;
  color: ${(props) => props.theme.colors.primary};
  margin-left: 8px;
`;

export const QuantityContainer = styled(View)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
`;

export const QuantityButton = styled(Pressable)`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: ${(props) => props.theme.colors.card};
  justify-content: center;
  align-items: center;
`;

export const QuantityValue = styled(Text)`
  font-size: 18px;
  font-weight: bold;
  color: ${(props) => props.theme.colors.text};
  margin-horizontal: 16px;
`;

export const AddToCartButton = styled(Button)``;

export const AddToCartText = styled(Text)`
  color: ${(props) => props.theme.colors.white};
  font-size: 16px;
  font-weight: bold;
`;

export const BottomContainer = styled(View)`
  padding: 16px;
  background-color: ${(props) => props.theme.colors.background};
  border-top-width: 1px;
  border-top-color: ${(props) => props.theme.colors.border};
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
`;

export const ShareButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  padding: 8px 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.card};
`;

export const ShareButtonText = styled(Text)`
  color: ${(props) => props.theme.colors.primary};
  font-size: 14px;
  margin-left: 8px;
`;

export const StockStatus = styled(View)`
  padding: 4px 12px;
  border-radius: 12px;
  background-color: ${(props) => props.theme.colors.error + "20"};
`;

export const StockText = styled(Text)`
  color: ${(props) => props.theme.colors.error};
  font-size: 12px;
  font-weight: 500;
  width: auto;
`;

export const ShortDescription = styled(View)`
  margin-bottom: 16px;
`;

export const ShortDescriptionText = styled(Text)`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text};
  line-height: 20px;
`;

export const MediaContainer = styled(View)`
  margin-top: 8px;
`;

export const MediaButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: ${(props) => props.theme.colors.card};
  margin-right: 8px;
`;

export const MediaButtonText = styled(Text)`
  color: ${(props) => props.theme.colors.primary};
  font-size: 14px;
  margin-top: 4px;
`;

export const MediaSection = styled(Section)``;
