import { styled } from "@/utils/styled";
import { View, Text, Pressable } from "react-native";

export const Container = styled(Pressable)<{ disabled?: boolean }>`
  flex-direction: row;
  align-items: center;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 12px;
  margin-bottom: 12px;
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.05;
  shadow-radius: 4px;
  elevation: 2;
  position: relative;
  opacity: ${({ disabled }) => (disabled ? 0.6 : 1)};
`;

export const ItemDetails = styled(View)`
  flex: 1;
  margin-left: 12px;
  gap: 8px;
`;

export const ItemName = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  line-height: 22px;
`;

export const RemoveButton = styled(Pressable)<{ isOutOfStock?: boolean }>`
  padding: 8px;
  margin-left: 12px;
  border-radius: 8px;
  background-color: ${({ theme, isOutOfStock }) =>
    isOutOfStock ? theme.colors.error + "20" : theme.colors.error + "10"};
  border: ${({ theme, isOutOfStock }) =>
    isOutOfStock ? `1px solid ${theme.colors.error + "40"}` : "none"};
  z-index: 2;
`;

export const OutOfStockOverlay = styled(View)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0; /* Leave space for remove button */
  bottom: 0;
  background-color: ${({ theme }) => theme.colors.background + "CC"};
  border-radius: 12px 0 0 12px;
  justify-content: center;
  align-items: center;
  z-index: 1;
`;

export const OutOfStockText = styled(Text)`
  font-size: 14px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.error};
  text-align: center;
  padding: 8px 16px;
  background-color: ${({ theme }) => theme.colors.error + "20"};
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.colors.error + "40"};
`;
