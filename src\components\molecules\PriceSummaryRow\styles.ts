import { styled } from "@/utils/styled";
import { View, Text } from "react-native";

export const Container = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
`;

export const LabelText = styled(Text)<{ variant?: "normal" | "bold" }>`
  font-size: 16px;
  font-weight: ${({ variant }) => (variant === "bold" ? "700" : "500")};
  flex: 1;
  color: ${({ theme }) => theme.colors.text};
`;
