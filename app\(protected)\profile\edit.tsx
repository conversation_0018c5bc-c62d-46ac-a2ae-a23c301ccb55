import { useRouter } from "expo-router";
import { RootState, useAppDispatch, useAppSelector } from "@/store/store";
import {
  getCityListAction,
  getStateListAction,
  updateUserAction,
} from "@/store/actions/auth";
import { useTranslation } from "react-i18next";
import { UpdateUserPayload } from "@/types/auth";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Content } from "@/styles/Profile.styles";
import { Header, ProfileEditForm } from "@/components";
import Toast from "react-native-toast-message";
import { updateUser } from "@/store/slices/authSlice";
import FormTemplate from "@/template/FormTemplate";
import { Fragment, useEffect } from "react";

const GSTIN_REGEX = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][A-Z0-9]Z[A-Z0-9]$/;
const PHONE_REGEX = /^[0-9]{10}$/;

export interface ProfileEditFormData {
  first_name: string;
  last_name: string;
  email: string;
  mobile: string;
  address_line_one: string;
  address_line_two: string;
  post_code: string;
  gst_number: string;
  country_id: number;
  state_id: number;
  city_id: number;
}

const schema = yup.object().shape({
  first_name: yup
    .string()
    .required("First name is required")
    .min(2, "First name must be at least 2 characters")
    .max(50, "First name must not exceed 50 characters")
    .matches(/^[a-zA-Z\s]*$/, "First name can only contain letters and spaces"),
  last_name: yup
    .string()
    .required("Last name is required")
    .min(2, "Last name must be at least 2 characters")
    .max(50, "Last name must not exceed 50 characters")
    .matches(/^[a-zA-Z\s]*$/, "Last name can only contain letters and spaces"),
  email: yup
    .string()
    .nullable()
    .transform((value) => (value === "" ? null : value))
    .test("email", "Please enter a valid email address", (value) => {
      if (!value) return true; // Allow empty
      return /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(value);
    })
    .max(100, "Email must not exceed 100 characters"),
  mobile: yup
    .string()
    .nullable()
    .transform((value) => (value === "" ? null : value))
    .test("mobile", "Please enter a valid 10-digit mobile number", (value) => {
      if (!value) return true; // Allow empty
      return PHONE_REGEX.test(value);
    }),
  address_line_one: yup
    .string()
    .nullable()
    .transform((value) => (value === "" ? null : value))
    .test(
      "address",
      "Address line one must be at least 5 characters",
      (value) => {
        if (!value) return true; // Allow empty
        return value.length >= 5 && value.length <= 100;
      }
    ),
  address_line_two: yup
    .string()
    .nullable()
    .transform((value) => (value === "" ? null : value))
    .test(
      "address",
      "Address line two must be at least 5 characters",
      (value) => {
        if (!value) return true; // Allow empty
        return value.length >= 5 && value.length <= 100;
      }
    ),
  post_code: yup
    .string()
    .nullable()
    .transform((value) => (value === "" ? null : value))
    .test("postcode", "Post code must be exactly 6 digits", (value) => {
      if (!value) return true; // Allow empty
      return /^\d{6}$/.test(value);
    }),
  gst_number: yup
    .string()
    .nullable()
    .transform((value) => (value === "" ? null : value))
    .test("gst", "Please enter a valid GSTIN number", (value) => {
      if (!value) return true; // Allow empty
      return GSTIN_REGEX.test(value);
    }),
  country_id: yup
    .number()
    .nullable()
    .transform((value) => (value === 0 ? null : value))
    .test("country", "Please select a country", (value) => {
      if (!value) return true; // Allow empty
      return value >= 1;
    }),
  state_id: yup
    .number()
    .nullable()
    .transform((value) => (value === 0 ? null : value))
    .test("state", "Please select a state", (value) => {
      if (!value) return true; // Allow empty
      return value >= 1;
    }),
  city_id: yup
    .number()
    .nullable()
    .transform((value) => (value === 0 ? null : value))
    .test("city", "Please select a city", (value) => {
      if (!value) return true; // Allow empty
      return value >= 1;
    }),
});

export default function EditProfileScreen() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const { user, isUpdateUserLoading } = useAppSelector(
    (state: RootState) => state.auth
  );

  const onSubmit = async (data: ProfileEditFormData) => {
    try {
      const response = await dispatch(
        updateUserAction({
          ...user,
          ...data,
        })
      ).unwrap();
      dispatch(updateUser(data as UpdateUserPayload));
      if (response.status) {
        Toast.show({
          type: "success",
          text1: response.message,
        });
        router.back();
      }
    } catch (error) {
      Toast.show({
        type: "error",
        text1: error?.message,
      });
    }
  };

  useEffect(() => {
    if (user?.country_id) {
      dispatch(getStateListAction(user?.country_id));
    }
    if (user?.state_id) {
      dispatch(getCityListAction(user?.state_id));
    }
  }, [dispatch, user?.country_id, user?.state_id]);

  return (
    <Fragment>
      <Header
        title={t("profile.editTitle")}
        showBack
        onBackPress={() => router.back()}
        showCart={false}
      />
      <Content showsVerticalScrollIndicator={false}>
        <FormTemplate<ProfileEditFormData>
          Component={(props) => (
            <ProfileEditForm {...props} loading={isUpdateUserLoading} />
          )}
          onSubmit={onSubmit}
          defaultValues={{
            first_name: user?.first_name || "",
            last_name: user?.last_name || "",
            email: user?.email || "",
            mobile: user?.mobile || "",
            address_line_one: user?.address_line_one || "",
            address_line_two: user?.address_line_two || "",
            post_code: user?.post_code || "",
            gst_number: user?.gst_number || (__DEV__ ? "27ABCDE1234F1Z5" : ""),
            country_id: user?.country_id || 0,
            state_id: user?.state_id || 0,
            city_id: user?.city_id || 0,
          }}
          resolver={yupResolver(schema)}
          mode="onChange"
        />
      </Content>
    </Fragment>
  );
}
