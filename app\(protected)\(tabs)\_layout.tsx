import { Ionicons } from "@expo/vector-icons";
import { Tabs } from "expo-router";
import { View } from "react-native";
import { Header } from "@/components";
import { useTheme } from "@/hooks/useTheme";
import { OrderIconCircle } from "@/styles/Tabbar.styles";
import { useAppSelector } from "@/store/store";
import { UserType } from "@/types/api";

export default function ProtectedTabsLayout() {
  const { theme } = useTheme();
  const { user } = useAppSelector((state) => state.auth);
  const isServicePerson = user?.role_id === UserType.SERVICEPERSON;

  return (
    <View style={{ flex: 1 }}>
      <Header title="Ozone Batteries" showCart={!isServicePerson} />
      <Tabs
        key={isServicePerson ? 'service' : 'customer'}
        initialRouteName="home"
        screenOptions={{
          headerShown: false,
          tabBarActiveTintColor: theme.colors.primary,
          tabBarInactiveTintColor: theme.colors.gray,
          tabBarStyle: {
            borderTopWidth: 1,
            borderTopColor: theme.colors.border,
            backgroundColor: theme.colors.background,
          },
        }}
      >
        <Tabs.Screen
          name="home"
          options={{
            title: "Home",
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="home-outline" size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="orders"
          options={{
            title: "Orders",
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="cube-outline" size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="products"
          options={{
            title: "Shop",
            tabBarIcon: ({ color, size, focused }) => (
              <OrderIconCircle focused={focused} size={size}>
                <Ionicons
                  name="cart-outline"
                  size={size * 0.9}
                  color={focused ? '#fff' : color}
                />
              </OrderIconCircle>
            ),
            href: isServicePerson ? null : undefined,
          }}
        />
        <Tabs.Screen
          name="complaints"
          options={{
            title: "Complaints",
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="warning-outline" size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="settings"
          options={{
            title: "Settings",
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="settings-outline" size={size} color={color} />
            ),
          }}
        />
      </Tabs>
    </View>
  );
}
