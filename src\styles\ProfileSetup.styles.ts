import { styled } from "../utils/styled";

export const Container = styled.View`
  flex: 1;
  padding: 24px;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const Title = styled.Text`
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: ${({ theme }) => theme.colors.text};
`;

export const InputContainer = styled.View`
  margin-bottom: 16px;
`;

export const Input = styled.TextInput`
  height: 50px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  padding-horizontal: 12px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  background-color: ${({ theme }) => theme.colors.background};
`;

export const PickerButton = styled.Pressable`
  height: 50px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  padding-horizontal: 12px;
  justify-content: center;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const PickerText = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const ErrorText = styled.Text`
  color: ${({ theme }) => theme.colors.error};
  font-size: 12px;
  margin-top: 4px;
`;

export const DisabledInput = styled(Input)`
  opacity: 0.5;
`;

export const DisabledPickerButton = styled(PickerButton)`
  opacity: 0.5;
`;

export const SubmitButton = styled.Pressable<{ disabled?: boolean }>`
  background-color: ${({ theme }) => theme.colors.primary};
  padding: 16px;
  border-radius: 8px;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 20px;
  opacity: ${({ disabled }) => (disabled ? 0.7 : 1)};
`;

export const SubmitButtonText = styled.Text`
  color: ${({ theme }) => theme.colors.white};
  font-size: 16px;
  font-weight: 600;
`;
