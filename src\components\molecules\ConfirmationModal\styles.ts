import { styled } from "@/utils/styled";
import { View, Text, Pressable } from "react-native";
import { ConfirmationVariant } from "./index";

interface ModalContentProps {
  variant: ConfirmationVariant;
}

interface IconContainerProps {
  variant: ConfirmationVariant;
}

interface ModalTitleProps {
  variant: ConfirmationVariant;
}

interface ConfirmButtonProps {
  variant: ConfirmationVariant;
  disabled?: boolean;
}

interface ButtonTextProps {
  variant: 'cancel' | 'confirm';
}

export const ModalOverlay = styled(View)`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

export const ModalContent = styled(View)<ModalContentProps>`
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 20px;
  padding: 28px 24px 24px 24px;
  width: 100%;
  max-width: 340px;
  align-items: center;
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 10px;
  shadow-opacity: 0.25;
  shadow-radius: 20px;
  elevation: 10;
  border-width: ${({ variant }) => variant === 'danger' ? '1px' : '0px'};
  border-color: ${({ theme, variant }) => 
    variant === 'danger' ? theme.colors.error + '20' : 'transparent'};
`;

export const IconContainer = styled(View)<IconContainerProps>`
  width: 64px;
  height: 64px;
  border-radius: 32px;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  background-color: ${({ theme, variant }) => {
    switch (variant) {
      case 'danger':
        return theme.colors.error + '15';
      case 'warning':
        return (theme.colors.warning || '#FF9500') + '15';
      case 'success':
        return theme.colors.success + '15';
      case 'info':
        return theme.colors.primary + '15';
      default:
        return theme.colors.primary + '15';
    }
  }};
  border: 2px solid ${({ theme, variant }) => {
    switch (variant) {
      case 'danger':
        return theme.colors.error + '30';
      case 'warning':
        return (theme.colors.warning || '#FF9500') + '30';
      case 'success':
        return theme.colors.success + '30';
      case 'info':
        return theme.colors.primary + '30';
      default:
        return theme.colors.primary + '30';
    }
  }};
`;

export const ModalTitle = styled(Text)<ModalTitleProps>`
  font-size: 22px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 12px;
  line-height: 28px;
  color: ${({ theme, variant }) => {
    switch (variant) {
      case 'danger':
        return theme.colors.error;
      default:
        return theme.colors.text;
    }
  }};
`;

export const ModalMessage = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  text-align: center;
  line-height: 24px;
  margin-bottom: 28px;
`;

export const ButtonContainer = styled(View)`
  flex-direction: row;
  width: 100%;
  gap: 12px;
`;

export const CancelButton = styled(Pressable)<{ disabled?: boolean }>`
  flex: 1;
  padding: 16px 20px;
  border-radius: 12px;
  border: 1.5px solid ${({ theme }) => theme.colors.gray};
  background-color: transparent;
  align-items: center;
  justify-content: center;
  min-height: 52px;
  opacity: ${({ disabled }) => disabled ? 0.6 : 1};
`;

export const ConfirmButton = styled(Pressable)<ConfirmButtonProps>`
  flex: 1;
  padding: 16px 20px;
  border-radius: 12px;
  align-items: center;
  justify-content: center;
  min-height: 52px;
  opacity: ${({ disabled }) => disabled ? 0.6 : 1};
  
  background-color: ${({ theme, variant }) => {
    switch (variant) {
      case 'danger':
        return theme.colors.error;
      case 'warning':
        return theme.colors.warning || '#FF9500';
      case 'success':
        return theme.colors.success;
      case 'info':
        return theme.colors.info || theme.colors.primary;
      default:
        return theme.colors.primary;
    }
  }};
  
  shadow-color: ${({ theme, variant }) => {
    switch (variant) {
      case 'danger':
        return theme.colors.error;
      case 'warning':
        return theme.colors.warning || '#FF9500';
      case 'success':
        return theme.colors.success;
      case 'info':
        return theme.colors.info || theme.colors.primary;
      default:
        return theme.colors.primary;
    }
  }};
  shadow-offset: 0px 4px;
  shadow-opacity: 0.2;
  shadow-radius: 8px;
  elevation: 4;
`;

export const ButtonText = styled(Text)<ButtonTextProps>`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme, variant }) => 
    variant === 'cancel' ? theme.colors.text : theme.colors.white};
`;
