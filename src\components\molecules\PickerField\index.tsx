import React, { useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { MaterialIcons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import Text from "../../atoms/Text";
import PickerBottomSheet, { PickerOption } from "./PickerBottomSheet";
import {
  Container,
  PickerButton,
  PickerText,
  DisabledPickerButton,
} from "./styles";

interface PickerFieldProps {
  name: string;
  label?: string;
  placeholder?: string;
  options: Array<{ id: number; [key: string]: any }>;
  onSelect?: (item: any) => boolean | void | Promise<boolean> | Promise<void>;
  disabled?: boolean;
  displayKey?: string;
  isLoading?: boolean;
  renderItem?: (item: PickerOption) => React.ReactNode;
  rules?: any;
}

const PickerField: React.FC<PickerFieldProps> = ({
  name,
  label,
  placeholder = "Select an option",
  options,
  onSelect,
  disabled = false,
  displayKey = "name",
  isLoading = false,
  renderItem,
  rules,
}) => {
  const {
    control,
    formState: { errors },
    setValue,
    watch,
  } = useFormContext();
  const { theme } = useTheme();
  const [showPicker, setShowPicker] = useState(false);
  const error = errors[name];
  const value = watch(name);

  const selectedOption = options.find((option) => option.id === value);

  const handleSelect = async (option: PickerOption) => {
    setValue(name, option.id);
    const shouldClose = await onSelect?.(option);
    // Only close the picker if onSelect returns true or if onSelect is not provided
    if (shouldClose !== false) {
      setShowPicker(false);
    }
  };

  const ButtonComponent = disabled ? DisabledPickerButton : PickerButton;

  return (
    <Container>
      {label && (
        <Text>
          {label}
          {rules?.required && <Text variant="error"> *</Text>}
        </Text>
      )}
      <Controller
        control={control}
        name={name}
        render={() => (
          <ButtonComponent
            onPress={() => !disabled && setShowPicker(true)}
            disabled={disabled}
          >
            <PickerText disabled={disabled}>
              {selectedOption ? selectedOption[displayKey] : placeholder}
            </PickerText>
            <MaterialIcons
              name="keyboard-arrow-down"
              size={20}
              color={disabled ? theme.colors.gray : theme.colors.text}
            />
          </ButtonComponent>
        )}
      />
      {error && <Text variant="error">{error.message as string}</Text>}

      <PickerBottomSheet
        isVisible={showPicker}
        onClose={() => setShowPicker(false)}
        onSelect={handleSelect}
        data={options}
        title={label || "Select Option"}
        searchPlaceholder={`Search ${label?.toLowerCase() || "options"}`}
        displayKey={displayKey}
        isLoading={isLoading}
        renderItem={renderItem}
      />
    </Container>
  );
};

export default PickerField;
