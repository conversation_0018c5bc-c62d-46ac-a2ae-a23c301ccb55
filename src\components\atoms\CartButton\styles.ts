import { styled } from "@/utils/styled";
import { Pressable, Text } from "react-native";

interface StyledButtonProps {
  variant: "primary" | "secondary" | "danger";
  size: "small" | "medium" | "large";
  fullWidth: boolean;
  disabled?: boolean;
}

interface ButtonTextProps {
  variant: "primary" | "secondary" | "danger";
  size: "small" | "medium" | "large";
}

export const StyledButton = styled(Pressable)<StyledButtonProps>`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  opacity: ${({ disabled }) => (disabled ? 0.6 : 1)};
  ${({ fullWidth }) => fullWidth && "flex: 1;"}

  padding: ${({ size }) => {
    switch (size) {
      case "small":
        return "8px 16px";
      case "large":
        return "16px 24px";
      default:
        return "12px 20px";
    }
  }};

  background-color: ${({ theme, variant }) => {
    switch (variant) {
      case "primary":
        return theme.colors.primary;
      case "danger":
        return theme.colors.error;
      case "secondary":
        return "transparent";
      default:
        return theme.colors.primary;
    }
  }};

  border-width: ${({ variant }) => (variant === "secondary" ? "1px" : "0px")};
  border-color: ${({ theme, variant }) =>
    variant === "secondary" ? theme.colors.gray : "transparent"};

  shadow-color: ${({ theme, variant }) =>
    variant !== "secondary" ? theme.colors.shadow : "transparent"};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: ${({ variant }) => (variant !== "secondary" ? 2 : 0)};
`;

export const ButtonText = styled(Text)<ButtonTextProps>`
  font-weight: 600;
  text-align: center;

  font-size: ${({ size }) => {
    switch (size) {
      case "small":
        return "14px";
      case "large":
        return "18px";
      default:
        return "16px";
    }
  }};

  color: ${({ theme, variant }) => {
    switch (variant) {
      case "secondary":
        return theme.colors.text;
      default:
        return theme.colors.white;
    }
  }};
`;
