import React from "react";
import { useFormContext, Controller } from "react-hook-form";
import ToggleSwitch from "../../atoms/ToggleSwitch";
import { Container, ErrorText } from "./styles";

interface SwitchFieldProps {
  name: string;
  label: string;
  color: string;
  disabled?: boolean;
  size?: "small" | "medium" | "large";
  testID?: string;
}

const SwitchField: React.FC<SwitchFieldProps> = ({
  name,
  label,
  color,
  disabled = false,
  size = "medium",
  testID,
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const error = errors[name];

  return (
    <Container>
      <Controller
        control={control}
        name={name}
        render={({ field: { onChange, value } }) => (
          <ToggleSwitch
            value={!!value}
            onValueChange={onChange}
            label={label}
            disabled={disabled}
            size={size}
            testID={testID}
          />
        )}
      />
      {error && <ErrorText>{error.message as string}</ErrorText>}
    </Container>
  );
};

export default SwitchField;
