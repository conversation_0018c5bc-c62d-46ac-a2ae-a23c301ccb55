import { styled } from "@/utils/styled";

export const Container = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const ErrorText = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.error};
  text-align: center;
  margin-bottom: 20px;
`;

export const RetryButton = styled.Pressable`
  background-color: ${({ theme }) => theme.colors.primary};
  padding: 12px 24px;
  border-radius: 8px;
`;

export const RetryText = styled.Text`
  color: ${({ theme }) => theme.colors.white};
  font-size: 16px;
  font-weight: 600;
`;
