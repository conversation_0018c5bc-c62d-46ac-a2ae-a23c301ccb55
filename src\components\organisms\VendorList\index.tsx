import React, { useCallback } from "react";
import { FlatList, ListRenderItem, View } from "react-native";
import { VendorDetails } from "@/types/vendor";
import { VendorCard } from "@/components/molecules";
import { LoadingOverlay } from "@/components/atoms";

interface VendorListProps {
  vendors: VendorDetails[];
  onEdit: (vendor: VendorDetails) => void;
  onDelete: (vendor: VendorDetails) => void;
  refreshing?: boolean;
  onRefresh?: () => void;
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  loading?: boolean;
  loadingMore?: boolean;
}

const VendorList: React.FC<VendorListProps> = ({
  vendors,
  onEdit,
  onDelete,
  refreshing = false,
  onRefresh,
  onEndReached,
  onEndReachedThreshold = 0.1,
  loading = false,
  loadingMore = false,
}) => {
  const renderVendorItem: ListRenderItem<VendorDetails> = useCallback(
    ({ item }) => (
      <VendorCard
        vendor={item}
        onEdit={() => onEdit(item)}
        onDelete={() => onDelete(item)}
      />
    ),
    [onEdit, onDelete]
  );

  const keyExtractor = useCallback(
    (item: VendorDetails) => item.vendor_id?.toString() || "",
    []
  );

  const handleEndReached = useCallback(() => {
    if (!loading && !loadingMore && onEndReached) {
      onEndReached();
    }
  }, [loading, loadingMore, onEndReached]);

  const renderFooter = useCallback(() => {
    if (loadingMore) {
      return (
        <View style={{ paddingVertical: 30 }}>
          <LoadingOverlay isLoading={true} size="small" />
        </View>
      );
    }
    return null;
  }, [loadingMore]);

  return (
    <FlatList
      data={vendors}
      renderItem={renderVendorItem}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      refreshing={refreshing}
      onRefresh={onRefresh}
      onEndReached={handleEndReached}
      onEndReachedThreshold={onEndReachedThreshold}
      ListFooterComponent={renderFooter}
      contentContainerStyle={{
        paddingVertical: 24,
      }}
    />
  );
};

export default VendorList;
