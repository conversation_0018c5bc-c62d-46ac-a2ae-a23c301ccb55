import React from 'react';
import { QuantityButton, Text } from '@/components/atoms';
import { Container, QuantityText } from './styles';

interface QuantitySelectorProps {
  quantity: number;
  onIncrement: () => void;
  onDecrement: () => void;
  minQuantity?: number;
  maxQuantity?: number;
  disabled?: boolean;
}

/**
 * QuantitySelector - Molecule component for quantity selection
 * Combines increment/decrement buttons with quantity display
 */
const QuantitySelector: React.FC<QuantitySelectorProps> = ({
  quantity,
  onIncrement,
  onDecrement,
  minQuantity = 1,
  maxQuantity = 999,
  disabled = false,
}) => {
  const canDecrement = quantity > minQuantity && !disabled;
  const canIncrement = quantity < maxQuantity && !disabled;

  return (
    <Container>
      <QuantityButton
        type="decrement"
        onPress={onDecrement}
        disabled={!canDecrement}
      />
      <QuantityText>
        {quantity}
      </QuantityText>
      <QuantityButton
        type="increment"
        onPress={onIncrement}
        disabled={!canIncrement}
      />
    </Container>
  );
};

export default QuantitySelector;
