import { styled } from "@/utils/styled";
import { View } from "react-native";

export const Container = styled(View)`
  background-color: ${({ theme }) => theme.colors.card};
  padding: 20px;
  border-radius: 12px;
  margin: 16px;
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.05;
  shadow-radius: 4px;
  elevation: 2;
`;

export const Divider = styled(View)`
  height: 1px;
  background-color: ${({ theme }) => theme.colors.divider};
  margin: 12px 0;
`;
