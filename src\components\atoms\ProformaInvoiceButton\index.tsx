import React, { useState } from "react";
import { Alert } from "react-native";
import * as WebBrowser from "expo-web-browser";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import { baseURl } from "@/services/api";
import Button from "@/components/atoms/Button";

interface ProformaInvoiceButtonProps {
  orderId: number;
  variant?: "primary" | "secondary" | "outline";
  size?: "small" | "medium" | "large";
}

/**
 * ProformaInvoiceButton - Atom component for opening proforma invoice
 * Opens the proforma invoice in device's web browser using Expo WebBrowser
 */
const ProformaInvoiceButton: React.FC<ProformaInvoiceButtonProps> = ({
  orderId,
  variant = "outline",
  size = "medium",
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);

  const handleOpenProformaInvoice = async () => {
    try {
      setLoading(true);
      const proformaUrl = `${baseURl}/proforma-invoice/${orderId}`;
      const result = await WebBrowser.openBrowserAsync(proformaUrl, {
        presentationStyle: WebBrowser.WebBrowserPresentationStyle.FULL_SCREEN,
        controlsColor: theme.colors.primary,
        toolbarColor: theme.colors.white,
      });

      // Handle the result if needed (user closed browser, etc.)
      if (result.type === "cancel") {
        console.log("User cancelled proforma invoice viewing");
      }
    } catch (error) {
      console.error("Error opening proforma invoice:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      onPress={handleOpenProformaInvoice}
      title={t("order.view_proforma", "View Proforma Invoice")}
      loading={loading}
      variant={variant}
      size={size}
      style={{
        flexDirection: "row",
        alignItems: "center",
        marginVertical: 8,
      }}
    />
  );
};

export default ProformaInvoiceButton;
