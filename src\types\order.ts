import * as ImagePicker from "expo-image-picker";
import { PaymentMethod } from "./payment";
import { ProductType } from "./product";

export interface OrderItem {
  name: string;
  quantity: number;
  price: number;
}
export enum OrderStatusEnum {
  Pending = 1,
  Approved = 2,
  InDispatch = 3,
  Dispatched = 4,
  Received = 5,
  Cancelled = 6,
}

export enum OrderType {
  Direct = 1,
  Complient = 2,
}

export enum OrderBy {
  OrderNumber = 1,
  OrderDate = 2,
  SubTotal = 3,
}
export enum SortOrder {
  ASC = 1,
  DESC = 2,
}

export enum OrderPaid {
  No = "0",
  Yes = "1",
}
export interface CartItem {
  id: number;
  quantity: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  user_id: number;
  product_id: number;
  price: string;
  qty: number;
  subtotal: number;
  cgst_percentage: string;
  sgst_percentage: string;
  igst_percentage: number;
  cgst: number;
  sgst: number;
  igst: number;
  total_amount: number;
  product: ProductType;
}

export interface CartList {
  cgst_percentage: number;
  sgst_percentage: number;
  igst_percentage: number;
  sub_total?: string;
  cgst_total?: string;
  sgst_total?: string;
  igst_total?: string;
  total_amount?: string;
  cartItems: CartItem[];
}

export interface OrderAddress {
  id: number;
  billing_shipping: string;
  contact_person_name: string;
  company_name: string;
  address_line_one: string;
  address_line_two: string;
  post_code: string;
  gst_number: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  country_id: number;
  state_id: number;
  city_id: number;
  user_id: number;
}

export interface OrderUser {
  id: number;
  first_name: string;
  last_name: string;
  name: string;
  email: string;
  email_verified_at: string | null;
  is_profile_complete: number;
  country_code_alpha: string;
  mobile: string;
  mobile_otp: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  role_id: number;
}

export interface OrderDetail {
  id: number;
  price: string;
  quantity: number;
  sub_total: string;
  cgst_percentage: string;
  sgst_percentage: string;
  igst_percentage: string;
  cgst_total: string;
  sgst_total: string;
  igst_total: string;
  total_amount: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  order_id: number;
  product_id: number;
  gst_rate_id: number | null;
  product: ProductType;
}

export interface Order {
  id: number;
  order_number: string;
  order_date: string;
  payment_terms: string | null;
  due_date: string | null;
  sub_total: string;
  discount_amount: string | null;
  cgst_percentage: string;
  sgst_percentage: string;
  igst_percentage: string;
  cgst_total: string;
  sgst_total: string;
  igst_total: string;
  total_amount: string;
  status: OrderStatusEnum;
  is_paid: string;
  notes: string | null;
  vehical_no: string | null;
  tracking: string | null;
  tracking_url: string | null;
  order_type: string;
  courier_name: string | null;
  delivery_date: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  billing_address_id: number;
  shipping_address_id: number;
  user_id: number;
  offer_id: number | null;
  billing_address: OrderAddress;
  shipping_address: OrderAddress;
  user: OrderUser;
  offer: any | null;
  eway_bill: any | null;
  order_detail: OrderDetail[];
}

export interface OrderPaginationLink {
  url: string | null;
  label: string;
  active: boolean;
}

export interface OrderPaginated {
  current_page: number;
  data: Order[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: OrderPaginationLink[];
  next_page_url: string | null;
  path: string;
  per_page: number;
  prev_page_url: string | null;
  to: number;
  total: number;
}

export interface OrderResponse {
  message: string;
  status: boolean;
  data: Order | Order[];
}

export interface OrderStatus {
  id: number;
  label: string;
}

export interface OrderState {
  cartList: CartList | null;
  orderDetails: Order | null;
  cartLoading: boolean;
  cartError: string | null;
  addToCartLoading: boolean;
  addToCartError: string | null;
  orders: Order[];
  ordersLoading: boolean;
  ordersError: string | null;
  orderStatusList: OrderStatus[] | null;
  couponCode?: string | null;
  current_page: number;
  last_page: number;
  loadedPages: number[];
  filters: OrderListPayloadType | null;
  selectedPaymentMethod: PaymentMethod | null;
  paymentProof: ImagePicker.ImagePickerAsset | null;
}

export interface AddToCartPayloadType {
  product_id: number;
  quantity: number;
}
export interface PlaceOrderPayloadType {
  billing_address_id: number;
  shipping_address_id: number;
  coupon_code?: string | null;
  order_type: OrderType;
  vendor_id?: number;
  payment_type: PaymentMethod;
  payment_proof?: ImagePicker.ImagePickerAsset;
}

export interface UpdateCartQuantityPayloadType {
  product_id: number;
  quantity: number;
}

export interface OrderListPayloadType {
  page?: number;
  order_type?: OrderType;
  status?: OrderStatusEnum;
  order_by?: OrderBy;
  sort_order?: SortOrder;
}
