import React, { useState } from "react";
import { Modal, ActivityIndicator, KeyboardAvoidingView, Platform, ScrollView } from "react-native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import {
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalTitle,
  CloseButton,
  FormContainer,
  FormField,
  FormLabel,
  FormTextArea,
  ButtonContainer,
  CancelButton,
  ConfirmButton,
  ButtonText,
} from "./styles";

export interface RejectData {
  reason: string;
}

interface RejectModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (data: RejectData) => Promise<void>;
  loading?: boolean;
}

const RejectModal: React.FC<RejectModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  loading = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [reason, setReason] = useState("");
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!reason.trim()) {
      newErrors.reason = t("order.rejection_reason_required");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleConfirm = async () => {
    if (!validateForm()) return;

    const rejectData: RejectData = {
      reason: reason.trim(),
    };

    try {
      await onConfirm(rejectData);
      handleCancel(); 
    } catch (error) {
    }
  };

  const handleCancel = () => {
    setReason("");
    setErrors({});
    onCancel();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleCancel}
    >
      <ModalOverlay>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{ width: "100%", maxWidth: 400 }}
        >
          <ModalContent>
            <ModalHeader>
              <ModalTitle>{t("order.reject_order")}</ModalTitle>
              <CloseButton onPress={handleCancel} disabled={loading}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </CloseButton>
            </ModalHeader>
          
              <FormContainer>
                <FormField>
                  <FormLabel>
                    {t("order.rejection_reason")} *
                  </FormLabel>
                  <FormTextArea
                    value={reason}
                    onChangeText={setReason}
                    placeholder={t("order.rejection_reason_placeholder")}
                    placeholderTextColor={theme.colors.gray}
                    multiline
                    numberOfLines={4}
                    textAlignVertical="top"
                    editable={!loading}
                    hasError={!!errors.reason}
                  />
                  {errors.reason && (
                    <FormLabel style={{ color: theme.colors.error, fontSize: 12, marginTop: 4 }}>
                      {errors.reason}
                    </FormLabel>
                  )}
                </FormField>
              </FormContainer>

            <ButtonContainer>
              <CancelButton onPress={handleCancel} disabled={loading}>
                <ButtonText variant="cancel">{t("common.cancel")}</ButtonText>
              </CancelButton>

              <ConfirmButton onPress={handleConfirm} disabled={loading}>
                {loading ? (
                  <ActivityIndicator size="small" color={theme.colors.white} />
                ) : (
                  <ButtonText variant="confirm">{t("order.reject")}</ButtonText>
                )}
              </ConfirmButton>
            </ButtonContainer>
          </ModalContent>
        </KeyboardAvoidingView>
      </ModalOverlay>
    </Modal>
  );
};

export default RejectModal;
