import React from "react";
import { OrderStatusEnum, OrderStatus } from "@/types/order";
import { OrderTimeline as TimelineComponent } from "@/components/molecules";
import { DispatchData } from "@/components/molecules/OrderTimeline";

interface OrderTimelineProps {
  status: OrderStatusEnum;
  orderStatusList: OrderStatus[];
  orderId?: number;
  onStatusChange?: (orderId: number, status: OrderStatusEnum, reason?: string) => Promise<void>;
  onDispatchStatusChange?: (orderId: number, status: OrderStatusEnum, dispatchData: DispatchData) => Promise<void>;
}

/**
 * OrderTimeline - Organism component for order timeline section
 * Displays order progress timeline with status updates and vendor approval buttons
 */
const OrderTimeline: React.FC<OrderTimelineProps> = ({
  status,
  orderStatusList,
  orderId,
  onStatusChange,
  onDispatchStatusChange,
}) => {
  return (
    <TimelineComponent
      currentStatus={status}
      orderStatusList={orderStatusList}
      orderId={orderId}
      onApprove={async (orderId) => {
        let nextStatus: OrderStatusEnum;
        
        if (status === OrderStatusEnum.Pending) {
          nextStatus = OrderStatusEnum.Approved;
        } else if (status === OrderStatusEnum.Approved) {
          nextStatus = OrderStatusEnum.InDispatch;
        } else if (status === OrderStatusEnum.Dispatched) {
          nextStatus = OrderStatusEnum.Received;
        } else {
          return;
        }
        await onStatusChange?.(orderId, nextStatus);
      }}
      onReject={async (orderId, reason) => {
        await onStatusChange?.(orderId, OrderStatusEnum.Cancelled, reason);
      }}
      onDispatch={async (orderId, dispatchData) => {
        if (onDispatchStatusChange) {
          await onDispatchStatusChange(orderId, OrderStatusEnum.Dispatched, dispatchData);
        } else {
          await onStatusChange?.(orderId, OrderStatusEnum.Dispatched);
        }
      }}
    />
  );
};

export default OrderTimeline;
