import React, { Fragment, useEffect, useRef } from "react";
import { MaterialIcons } from "@expo/vector-icons";
import { StepInfo, StepText, Separator, StepInfoContainer } from "./styles";
import { Spacer } from "@/styles/common.style";
import { useTheme } from "@/hooks/useTheme";
import { Dimensions, View, ScrollView } from "react-native";
import { OrderStatus } from "@/types/order";
const { width } = Dimensions.get("screen");

export interface StepperProps {
  stepData: OrderStatus[];
  currentId: number;
  setSelectedTabNav?: (id: number) => void;
}

const CurrentStep = () => {
  const { theme } = useTheme();
  return (
    <MaterialIcons
      name="radio-button-checked"
      size={24}
      color={theme.colors.primary}
    />
  );
};

const NextStep = () => {
  const { theme } = useTheme();
  return (
    <MaterialIcons
      name="radio-button-unchecked"
      size={24}
      color={theme.colors.gray}
    />
  );
};

const CheckCircle = () => {
  const { theme } = useTheme();
  return (
    <MaterialIcons name="check-circle" size={24} color={theme.colors.success} />
  );
};

const Stepper: React.FC<StepperProps> = ({
  stepData,
  currentId,
  setSelectedTabNav,
}) => {
  const scrollViewRef = useRef<ScrollView>(null);
  const stepRefs = useRef<{ [key: number]: View | null }>({});

  useEffect(() => {
    // Scroll to active step when currentId changes
    if (scrollViewRef.current && stepRefs.current[currentId]) {
      stepRefs.current[currentId]?.measureLayout(
        // @ts-ignore - measureLayout exists on View
        scrollViewRef.current,
        (x: number) => {
          scrollViewRef.current?.scrollTo({
            x: x - width / 4, // Center the active step
            animated: true,
          });
        },
        () => {}
      );
    }
  }, [currentId]);

  const renderIcon = (itemId: number) => {
    if (currentId === itemId) return <CurrentStep />;
    if (currentId < itemId) return <NextStep />;
    return <CheckCircle />;
  };

  const stepWidth = width / stepData.length - 12 * 5;
  return (
    <ScrollView
      ref={scrollViewRef}
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{ flexGrow: 1 }}
      scrollEnabled={stepData.length > 3}
    >
      {stepData.map((item, index) => (
        <Fragment key={item.id}>
          <StepInfoContainer
            onPress={() => setSelectedTabNav?.(item.id)}
            ref={(ref) => (stepRefs.current[item.id] = ref)}
          >
            <StepInfo>
              {renderIcon(item.id)}
              <Spacer size={4} />
              <StepText numberOfLines={2} isCurrentStep={currentId === item.id}>
                {item.label}
              </StepText>
            </StepInfo>
          </StepInfoContainer>
          {index < stepData.length - 1 && <Separator width={stepWidth - 15} />}
        </Fragment>
      ))}
    </ScrollView>
  );
};

export default Stepper;
