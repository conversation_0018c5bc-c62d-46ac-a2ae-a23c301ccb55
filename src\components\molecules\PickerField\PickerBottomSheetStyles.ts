import { styled } from "@/utils/styled";
import { BottomSheetSectionList } from "@gorhom/bottom-sheet";

export const ListContainer = styled(BottomSheetSectionList)``;

export const SectionHeaderContainer = styled.View`
  background-color: ${({ theme }) => theme.colors.background};
`;

export const Title = styled.Text`
  font-size: 20px;
  font-weight: 600;
  padding-horizontal: 16px;
  padding-top: 16px;
  margin-bottom: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const SearchContainer = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.inputBackground};
  border-radius: 8px;
  padding: 8px 12px;
  margin-horizontal: 16px;
  margin-bottom: 16px;
`;

export const SearchInput = styled.TextInput`
  flex: 1;
  margin-left: 8px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const PickerItem = styled.Pressable`
  padding: 12px 16px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.border};
`;

export const PickerItemText = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const EmptyContainer = styled.View`
  justify-content: center;
  align-items: center;
  padding: 20px;
  height: 200px;
`;

export const EmptyText = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const LoadingContainer = styled.View`
  justify-content: center;
  align-items: center;
  height: 200px;
`;
