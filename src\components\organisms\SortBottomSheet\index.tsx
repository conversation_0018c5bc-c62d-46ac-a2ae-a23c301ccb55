import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { BottomSheetModal, BottomSheetFlatList } from "@gorhom/bottom-sheet";
import { Ionicons } from "@expo/vector-icons";
import {
  StyledBottomSheetModal,
  StyledBottomSheetView,
  HeaderRow,
  Title,
  CloseButton,
  Divider,
  SortOption,
  SortOptionText,
  RadioButton,
  RadioButtonInner,
  ButtonRow,
  ClearButton,
  ClearText,
  ApplyButton,
  ApplyText,
  SortOptionsContainer,
} from "./styles";
import { useBottomSheetBackdrop } from "@/utils/commonBackdrop";
import { useTheme } from "@/hooks/useTheme";

export interface SortOptionList {
  label: string;
  order_by: number;
  sort_order?: number;
  sort_by?: number;
}

interface SortBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onApply: (sortOption: SortOptionList | null) => void;
  onDismiss: () => void;
  onClear: () => void;
  sortOptions: SortOptionList[];
  title?: string;
  clearText?: string;
  applyText?: string;
  selectedOptions?: SortOptionList | null;
}

export default function SortBottomSheet({
  isVisible,
  onApply,
  onDismiss,
  onClear,
  sortOptions,
  title = "Sort by",
  clearText = "Clear all",
  applyText = "Apply",
  selectedOptions,
}: SortBottomSheetProps) {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ["50%"], []);
  const [selectedOption, setSelectedOption] = useState<SortOptionList | null>(
    selectedOptions || null
  );
  const { theme } = useTheme();
  const handleApply = () => {
    onApply(selectedOption);
  };

  const handleClear = () => {
    setSelectedOption(null);
    onClear();
  };

  useEffect(() => {
    if (isVisible) {
      bottomSheetRef.current?.present();
    } else {
      bottomSheetRef.current?.dismiss();
    }
  }, [isVisible, sortOptions]);

  const renderSortOption = useCallback(
    ({ item }: { item: SortOptionList }) => {
      const isSelected = selectedOption?.label === item.label;

      return (
        <SortOption
          selected={isSelected}
          onPress={() => {
            setSelectedOption(item);
          }}
        >
          <SortOptionText selected={isSelected}>{item.label}</SortOptionText>
          <RadioButton selected={isSelected}>
            <RadioButtonInner selected={isSelected} />
          </RadioButton>
        </SortOption>
      );
    },
    [selectedOption]
  );
  const renderBackdrop = useBottomSheetBackdrop();

  return (
    <StyledBottomSheetModal
      ref={bottomSheetRef}
      index={0}
      snapPoints={snapPoints}
      enableDynamicSizing={false}
      onDismiss={onDismiss}
      enablePanDownToClose
      backgroundStyle={{ backgroundColor: theme.colors.background }}
      backdropComponent={renderBackdrop}
      handleIndicatorStyle={{ backgroundColor: theme.colors.gray }}
    >
      <StyledBottomSheetView>
        <HeaderRow>
          <Title>{title}</Title>
          <CloseButton onPress={onDismiss}>
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </CloseButton>
        </HeaderRow>
        <Divider />
        <SortOptionsContainer>
          <BottomSheetFlatList
            data={sortOptions}
            keyExtractor={(item) => item.label}
            renderItem={renderSortOption}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              paddingBottom: 80,
            }}
          />
        </SortOptionsContainer>
        <ButtonRow>
          <ClearButton onPress={handleClear}>
            <ClearText>{clearText}</ClearText>
          </ClearButton>
          <ApplyButton onPress={handleApply}>
            <ApplyText>{applyText}</ApplyText>
          </ApplyButton>
        </ButtonRow>
      </StyledBottomSheetView>
    </StyledBottomSheetModal>
  );
}
