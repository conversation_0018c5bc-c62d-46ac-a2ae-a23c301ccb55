import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import en from "@/locales/en.json";
import gu from "@/locales/gu.json";
import hi from "@/locales/hi.json";

const resources = {
  en: { translation: en },
  gu: { translation: gu },
  hi: { translation: hi },
};

i18n.use(initReactI18next).init({
  resources,
  lng: "en", // default language
  fallbackLng: "en",
  interpolation: {
    escapeValue: false,
  },
  compatibilityJSON: "v4",
});

export default i18n;
