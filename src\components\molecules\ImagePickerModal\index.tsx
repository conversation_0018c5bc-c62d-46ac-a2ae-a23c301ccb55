import React, { useEffect } from "react";
import { <PERSON><PERSON>, StatusBar, ActivityIndicator } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import {
  ModalOverlay,
  ModalContainer,
  ModalHeader,
  ModalTitle,
  ModalContent,
  OptionContainer,
  OptionButton,
  OptionIcon,
  OptionText,
  CancelButton,
  CancelButtonText,
} from "./styles";
import { useTheme } from "@/hooks/useTheme";

interface ImagePickerModalProps {
  visible: boolean;
  onClose: () => void;
  onCameraPress: () => void;
  onGalleryPress: () => void;
  title?: string;
  isLoading?: boolean;
  loadingType?: 'camera' | 'gallery' | null;
}

const ImagePickerModal: React.FC<ImagePickerModalProps> = ({
  visible,
  onClose,
  onCameraPress,
  onGalleryPress,
  title = "Upload Payment Proof",
  isLoading = false,
  loadingType = null,
}) => {
  const { theme } = useTheme();

  useEffect(() => {
    if (visible) {
      StatusBar.setBackgroundColor(theme.colors.overlay, true);
    } else {
      StatusBar.setBackgroundColor(theme.colors.background, true);
    }
  }, [visible, theme.colors.background]);

  const handleCameraPress = () => {
    onCameraPress();
    if (!isLoading) {
      onClose();
    }
  };

  const handleGalleryPress = () => {
    onGalleryPress();
    if (!isLoading) {
      onClose();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
      statusBarTranslucent={false}
      hardwareAccelerated={true}
      presentationStyle="overFullScreen"
      supportedOrientations={['portrait']}
    >
      <ModalOverlay onPress={onClose} activeOpacity={1}>
        <ModalContainer onPress={(e: any) => e.stopPropagation()}>
          <ModalHeader>
            <ModalTitle>{title}</ModalTitle>
          </ModalHeader>

          <ModalContent>
            <OptionContainer>
              <OptionButton
                onPress={handleCameraPress}
                disabled={isLoading}
                style={{ opacity: isLoading && loadingType === 'camera' ? 0.6 : 1 }}
              >
                <OptionIcon>
                  {isLoading && loadingType === 'camera' ? (
                    <ActivityIndicator
                      size={32}
                      color={theme.colors.primary}
                    />
                  ) : (
                    <Ionicons
                      name="camera-outline"
                      size={32}
                      color={theme.colors.primary}
                    />
                  )}
                </OptionIcon>
                <OptionText>
                  {isLoading && loadingType === 'camera' ? 'Processing...' : 'Camera'}
                </OptionText>
              </OptionButton>

              <OptionButton
                onPress={handleGalleryPress}
                disabled={isLoading}
                style={{ opacity: isLoading && loadingType === 'gallery' ? 0.6 : 1 }}
              >
                <OptionIcon>
                  {isLoading && loadingType === 'gallery' ? (
                    <ActivityIndicator
                      size={32}
                      color={theme.colors.primary}
                    />
                  ) : (
                    <Ionicons
                      name="images-outline"
                      size={32}
                      color={theme.colors.primary}
                    />
                  )}
                </OptionIcon>
                <OptionText>
                  {isLoading && loadingType === 'gallery' ? 'Processing...' : 'Gallery'}
                </OptionText>
              </OptionButton>
            </OptionContainer>

            <CancelButton
              onPress={onClose}
              disabled={isLoading}
              style={{ opacity: isLoading ? 0.6 : 1 }}
            >
              <CancelButtonText>Cancel</CancelButtonText>
            </CancelButton>
          </ModalContent>
        </ModalContainer>
      </ModalOverlay>
    </Modal>
  );
};

export default ImagePickerModal;
