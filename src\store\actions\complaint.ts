import { createAsyncThunk } from "@reduxjs/toolkit";
import {
  Complaint,
  ComplaintResponse,
  CreateComplaintPayload,
  UpdateComplaintPayload,
  ResolveComplaintPayload,
  ComplaintStatusEnum,
} from "../../types/complaint";
import { withToastForError } from "../../utils/thunk";
import { sleep } from "../../utils/common";
import { ApiResponse } from "../../types/api";

export const getComplaintsAction = createAsyncThunk(
  "complaint/getAll",
  withToastForError(async (): Promise<ApiResponse> => {
    // const response = await api.get("/complaints");
    // TODO: Implement actual API call
    const response = (await sleep(2, {
      message: "Complaints fetched successfully",
      status: true,
      data: [
        {
          id: "1",
          status: ComplaintStatusEnum.Pending,
          description: "Battery not charging properly",
          serial_number: "SN123456",
          created_at: new Date().toISOString(),
          image: "https://picsum.photos/seed/hello/800/600",
        },
        {
          id: "2",
          status: ComplaintStatusEnum.RepairInProgress,
          description: "Battery overheating",
          serial_number: "SN789012",
          created_at: new Date().toISOString(),
          image: "https://picsum.photos/seed/hello/800/600",
        },
      ],
    })) as unknown as ComplaintResponse;
    return response;
  })
);

export const getComplaintDetailsAction = createAsyncThunk(
  "complaint/getDetails",
  withToastForError(async (complaintId: string): Promise<ApiResponse> => {
    // const response = await api.get(`/complaints/${complaintId}`);
    // TODO: Implement actual API call
    const response = (await sleep(2, {
      message: "Complaint details fetched successfully",
      status: true,
      data: {
        id: complaintId,
        status: ComplaintStatusEnum.Pending,
        description: "Battery not charging properly",
        serial_number: "SN123456",
        created_at: new Date().toISOString(),
        image: "https://picsum.photos/seed/hello/800/600",
        warranty_status: "partial_warranty",
        repair_cost: 1000,
      },
    })) as unknown as ComplaintResponse;
    return response;
  })
);

export const createComplaintAction = createAsyncThunk(
  "complaint/create",
  withToastForError(
    async (payload: CreateComplaintPayload): Promise<ApiResponse> => {
      // const response = await api.post("/complaints", payload);
      // TODO: Implement actual API call
      const response = (await sleep(2, {
        message: "Complaint created successfully",
        status: true,
        data: {
          id: Math.random().toString(),
          status: ComplaintStatusEnum.Pending,
          description: payload.description,
          serial_number: payload.serial_number,
          created_at: new Date().toISOString(),
        },
      })) as unknown as ComplaintResponse;
      return response;
    }
  )
);

export const updateComplaintAction = createAsyncThunk(
  "complaint/update",
  withToastForError(
    async (payload: UpdateComplaintPayload): Promise<ApiResponse> => {
      // const response = await api.put(`/complaints/${payload.id}`, payload);
      // TODO: Implement actual API call
      const response = (await sleep(2, {
        message: "Complaint updated successfully",
        status: true,
        data: {
          id: payload.id,
          status: payload.status || ComplaintStatusEnum.Pending,
          description: payload.description || "",
          serial_number: "",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      })) as unknown as ComplaintResponse;
      return response;
    }
  )
);

export const resolveComplaintAction = createAsyncThunk(
  "complaint/resolve",
  withToastForError(
    async (payload: ResolveComplaintPayload): Promise<ApiResponse> => {
      // const response = await api.put(`/complaints/${payload.id}/resolve`, {
      //   resolution: payload.resolution,
      // });
      // TODO: Implement actual API call
      const response = (await sleep(2, {
        message: "Complaint resolved successfully",
        status: true,
        data: {
          id: payload.id,
          status: ComplaintStatusEnum.Resolved,
          description: "",
          serial_number: "",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          resolution: payload.resolution,
          resolved_at: new Date().toISOString(),
        },
      })) as unknown as ComplaintResponse;
      return response;
    }
  )
);
