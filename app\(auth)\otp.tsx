import React from "react";
import { Image } from "react-native";
import { useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import {
  getUserDetailsAction,
  loginOTPAction,
  loginOTPVerifyAction,
} from "@/store/actions/auth";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { RootState } from "@/store/store";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import Toast from "react-native-toast-message";
import { TopContainer } from "@/styles/OTP.styles";
import { useTheme } from "@/hooks/useTheme";
import { OTPForm } from "@/components";

export default function OTPVerification() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { isVerifyLoading, user } = useAppSelector(
    (state: RootState) => state.auth
  );
  const { theme } = useTheme();

  const handleVerifyOTP = async (otpValue: string) => {
    try {
      const response = await dispatch(
        loginOTPVerifyAction({
          payload: {
            country_code_alpha: user?.country_code_alpha || "",
            mobile: user?.mobile || "",
            otp: otpValue,
          },
        })
      ).unwrap();
      if (response.status) {
        await dispatch(getUserDetailsAction({})).unwrap();
        Toast.show({
          type: "success",
          text1: t("common.otp_verified_success"),
        });
        router.replace("/");
      }
    } catch (error) {
      Toast.show({
        type: "error",
        text1: error?.message || t("common.error_message"),
      });
    }
  };

  const handleResendOTP = async () => {
    try {
      const response = await dispatch(
        loginOTPAction({
          country_code_alpha: user?.country_code_alpha,
          mobile: user?.mobile,
        })
      ).unwrap();

      Toast.show({
        type: "success",
        text1: response.message,
      });
    } catch (error) {
      Toast.show({
        type: "error",
        text1: error?.message || t("common.error_message"),
      });
    }
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <KeyboardAwareScrollView
      style={{ flex: 1, backgroundColor: theme.colors.background }}
      contentContainerStyle={{ flexGrow: 1 }}
      keyboardShouldPersistTaps="handled"
    >
      <TopContainer>
        <Image
          source={require("../../assets/icon.png")}
          style={{ height: "80%", width: "100%" }}
          resizeMode="contain"
        />
      </TopContainer>

      <OTPForm
        onSubmit={handleVerifyOTP}
        onResend={handleResendOTP}
        onBack={handleBack}
        loading={isVerifyLoading}
      />
    </KeyboardAwareScrollView>
  );
}
