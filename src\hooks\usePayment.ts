import { useState, useCallback } from "react";
import * as Clipboard from "expo-clipboard";
import * as FileSystem from "expo-file-system";
import * as MediaLibrary from "expo-media-library";
import {
  PaymentMethod,
  PAYMENT_METHODS_REQUIRING_PROOF,
} from "@/types/payment";
import Toast from "react-native-toast-message";
import * as ImagePicker from "expo-image-picker";
import { useFileValidation } from "./useFileValidation";
import { useAppDispatch, useAppSelector } from "@/store/store";
import {
  setSelectedPaymentMethod,
  setPaymentProof,
  clearPaymentProof,
} from "@/store/slices/orderSlice";

export const usePayment = () => {
  const dispatch = useAppDispatch();
  const { selectedPaymentMethod, paymentProof } = useAppSelector(
    (state) => state.orders
  );
  const { validateImageFile } = useFileValidation();

  const handleMethodSelect = useCallback(
    (method: PaymentMethod) => {
      if (selectedPaymentMethod === method) {
        dispatch(setSelectedPaymentMethod(null));
      } else {
        dispatch(setSelectedPaymentMethod(method));
      }
    },
    [dispatch, selectedPaymentMethod]
  );

  const handleCopyToClipboard = useCallback(async (text: string) => {
    try {
      await Clipboard.setStringAsync(text);
      Toast.show({
        type: "success",
        text1: `${text} copied!`,
      });
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "Failed to copy to clipboard",
      });
    }
  }, []);

  const handleDownloadQR = useCallback(async (url: string) => {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== "granted") {
        Toast.show({
          type: "error",
          text1: "Permission to access media library is required!",
        });
        return;
      }

      const fileUri = FileSystem.documentDirectory + "qr_code.jpg";
      const downloadResult = await FileSystem.downloadAsync(url, fileUri);

      if (downloadResult.status === 200) {
        await MediaLibrary.saveToLibraryAsync(downloadResult.uri);
        Toast.show({
          type: "success",
          text1: "QR code saved to gallery!",
        });
      } else {
        Toast.show({
          type: "error",
          text1: "Failed to download QR code",
        });
      }
    } catch (error) {
      console.error("Download error:", error);
      Toast.show({
        type: "error",
        text1: "Failed to download QR code",
      });
    }
  }, []);

  const validateAndSetPaymentProof = useCallback(
    async (asset: ImagePicker.ImagePickerAsset) => {
      const validation = await validateImageFile(asset);

      if (validation.isValid) {
        dispatch(setPaymentProof(asset));
        return true;
      }

      return false;
    },
    [validateImageFile, dispatch]
  );

  const handleRemovePaymentProof = useCallback(() => {
    dispatch(clearPaymentProof());
    Toast.show({
      type: "info",
      text1: "Payment proof removed",
    });
  }, [dispatch]);

  const setPaymentProofDirectly = useCallback(
    (asset: ImagePicker.ImagePickerAsset) => {
      dispatch(setPaymentProof(asset));
    },
    [dispatch]
  );

  const isPaymentProofRequired =
    selectedPaymentMethod &&
    PAYMENT_METHODS_REQUIRING_PROOF.includes(selectedPaymentMethod);
  const canContinue =
    selectedPaymentMethod && (!isPaymentProofRequired || paymentProof);

  return {
    selectedMethod: selectedPaymentMethod,
    paymentProof,
    handleMethodSelect,
    handleCopyToClipboard,
    handleDownloadQR,
    validateAndSetPaymentProof,
    handleRemovePaymentProof,
    isPaymentProofRequired,
    canContinue,
    setPaymentProof: setPaymentProofDirectly,
  };
};
