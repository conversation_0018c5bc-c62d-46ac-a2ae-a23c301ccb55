import React from "react";
import { useTranslation } from "react-i18next";
import {
  Container,
  Row,
  TotalRow,
  Label,
  Value,
  TotalLabel,
  TotalValue,
  TaxRow,
  TaxLabel,
  TaxValue,
} from "./styles";

interface PricingBreakdownProps {
  subtotal: string;
  cgstPercentage?: string;
  sgstPercentage?: string;
  igstPercentage?: string;
  cgstTotal?: string;
  sgstTotal?: string;
  igstTotal?: string;
  discountAmount?: string;
  totalAmount: string;
}

const PricingBreakdown: React.FC<PricingBreakdownProps> = ({
  subtotal,
  cgstPercentage,
  sgstPercentage,
  igstPercentage,
  cgstTotal,
  sgstTotal,
  igstTotal,
  discountAmount,
  totalAmount,
}) => {
  const { t } = useTranslation();

  const formatCurrency = (amount: string | number) => {
    return `₹${parseFloat(amount.toString()).toFixed(2)}`;
  };

  const hasTaxes = cgstTotal || sgstTotal || igstTotal;

  return (
    <Container>
      <Row>
        <Label>{t("order.subtotal")}</Label>
        <Value>{formatCurrency(subtotal)}</Value>
      </Row>
      {discountAmount && parseFloat(discountAmount) > 0 && (
        <Row>
          <Label>{t("order.discount")}</Label>
          <Value>{formatCurrency(discountAmount)}</Value>
        </Row>
      )}
      {hasTaxes && (
        <>
          <Row>
            <Label>{t("order.taxes")}</Label>
            <Value>
              {formatCurrency(
                (
                  parseFloat(cgstTotal || "0") +
                  parseFloat(sgstTotal || "0") +
                  parseFloat(igstTotal || "0")
                ).toFixed(2)
              )}
            </Value>
          </Row>

          {cgstTotal && parseFloat(cgstTotal) > 0 && (
            <TaxRow>
              <TaxLabel>CGST ({cgstPercentage}%)</TaxLabel>
              <TaxValue>{formatCurrency(cgstTotal)}</TaxValue>
            </TaxRow>
          )}

          {sgstTotal && parseFloat(sgstTotal) > 0 && (
            <TaxRow>
              <TaxLabel>SGST ({sgstPercentage}%)</TaxLabel>
              <TaxValue>{formatCurrency(sgstTotal)}</TaxValue>
            </TaxRow>
          )}

          {igstTotal && parseFloat(igstTotal) > 0 && (
            <TaxRow>
              <TaxLabel>IGST ({igstPercentage}%)</TaxLabel>
              <TaxValue>{formatCurrency(igstTotal)}</TaxValue>
            </TaxRow>
          )}
        </>
      )}

      <TotalRow>
        <TotalLabel>{t("order.total")}</TotalLabel>
        <TotalValue>{formatCurrency(totalAmount)}</TotalValue>
      </TotalRow>
    </Container>
  );
};

export default PricingBreakdown;
