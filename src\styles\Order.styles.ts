import { OrderStatusEnum } from "@/types/order";
import { styled } from "../utils/styled";
import { View, Text, Pressable } from "react-native";
import { Image } from "expo-image";

export const Container = styled(View)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const Content = styled(View)`
  flex: 1;
  padding: 16px;
`;

export const Header = styled.View`
  padding: 16px;
`;

export const ActiveFiltersContainer = styled.View`
  padding: 0 16px 8px;
`;

export const FilterChipsRow = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
`;

export const ActiveFilterChip = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.primary};
  padding: 6px 10px;
  border-radius: 16px;
  gap: 4px;
`;

export const ActiveFilterText = styled.Text`
  color: ${({ theme }) => theme.colors.white};
  font-size: 14px;
`;

export const Card = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.cardbackground};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  elevation: 2;
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
`;

export const Title = styled(Text)`
  font-size: 20px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 16px;
`;

export const Label = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  margin-bottom: 8px;
`;

export const Value = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 16px;
`;

export const StatusBadge = styled(View)<{ status: OrderStatusEnum }>`
  padding: 6px 12px;
  border-radius: 16px;
  background-color: ${(props) => {
    switch (props.status) {
      case OrderStatusEnum.Pending:
        return props.theme.colors.statusPendingBg;
      case OrderStatusEnum.Approved:
        return props.theme.colors.statusInProgressBg;
      case OrderStatusEnum.Dispatched:
        return props.theme.colors.statusResolvedBg;
      case OrderStatusEnum.Received:
        return props.theme.colors.statusRejectedBg;
      case OrderStatusEnum.Cancelled:
        return props.theme.colors.statusRejectedBg;
      default:
        return props.theme.colors.lightGray;
    }
  }};
`;

export const StatusText = styled(Text)<{ status: OrderStatusEnum }>`
  font-size: 14px;
  color: ${(props) => {
    switch (props.status) {
      case OrderStatusEnum.Pending:
        return props.theme.colors.statusPending;
      case OrderStatusEnum.Approved:
        return props.theme.colors.statusInProgress;
      case OrderStatusEnum.Dispatched:
        return props.theme.colors.statusResolved;
      case OrderStatusEnum.Received:
        return props.theme.colors.statusRejected;
      case OrderStatusEnum.Cancelled:
        return props.theme.colors.statusRejected;
      default:
        return props.theme.colors.gray;
    }
  }};
`;

export const ActionButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.secondary};
  border-radius: 8px;
  padding: 12px;
  align-items: center;
  margin-top: 8px;
`;

export const ActionButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 14px;
  font-weight: 600;
`;

export const CancelButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.error};
  border-radius: 8px;
  padding: 12px;
  align-items: center;
  margin-top: 8px;
`;

export const CancelButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 14px;
  font-weight: 600;
`;

export const LoadingContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
`;

export const LoadingText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  margin-top: 16px;
`;

export const ErrorText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.error};
  text-align: center;
`;

export const EmptyContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 32px;
`;

export const EmptyText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
  margin-top: 16px;
`;

export const ProductItem = styled(View)`
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
`;

export const ProductImage = styled(View)`
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.lightGray};
  margin-right: 12px;
  overflow: hidden;
`;

export const StyledProductImage = styled(Image)`
  width: 100%;
  height: 100%;
`;

export const ProductInfo = styled(View)`
  flex: 1;
`;

export const ProductName = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
`;

export const ProductQuantity = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
`;

export const TotalContainer = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.divider};
`;

export const TotalLabel = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
`;

export const TotalValue = styled(Text)`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.secondary};
`;

export const SearchBarContainer = styled(View)`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.inputBackground};
  border-radius: 24px;
  padding-horizontal: 16px;
  margin: 16px;
  height: 44px;
`;

export const InfoRow = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-horizontal: 16px;
  margin-bottom: 8px;
`;

export const ItemsCountText = styled(Text)`
  color: ${({ theme }) => theme.colors.text};
  font-size: 15px;
`;

export const SortButtonRow = styled(Pressable)`
  flex-direction: row;
  align-items: center;
`;

export const SortLabel = styled(Text)`
  color: ${({ theme }) => theme.colors.text};
  font-size: 15px;
`;

export const SortValueText = styled(Text)`
  font-weight: bold;
  color: ${({ theme }) => theme.colors.primary};
  font-size: 15px;
`;
