import React from "react";
import { useTranslation } from "react-i18next";
import { PriceSummaryRow } from "@/components/molecules";
import { Container, Divider } from "./styles";

interface CartSummaryData {
  sub_total?: string | number;
  cgst_total?: string | number;
  cgst_percentage?: string | number;
  sgst_total?: string | number;
  sgst_percentage?: string | number;
  igst_total?: string | number;
  igst_percentage?: string | number;
  total_amount?: string | number;
}

interface CartSummaryProps {
  data: CartSummaryData;
}

/**
 * CartSummary - Organism component for cart price breakdown
 * Displays subtotal, taxes, and total amount
 */
const CartSummary: React.FC<CartSummaryProps> = ({ data }) => {
  const { t } = useTranslation();

  if (!data) {
    return null;
  }

  return (
    <Container>
      <PriceSummaryRow
        label={t("cart.subtotal")}
        amount={data.sub_total || "0"}
      />

      <PriceSummaryRow
        label={t("cart.cgst")}
        amount={data.cgst_total || "0"}
        showPercentage
        percentage={data.cgst_percentage || "0"}
      />

      <PriceSummaryRow
        label={t("cart.sgst")}
        amount={data.sgst_total || "0"}
        showPercentage
        percentage={data.sgst_percentage || "0"}
      />

      {parseFloat(String(data.igst_total || "0")) > 0 && (
        <PriceSummaryRow
          label={t("cart.igst")}
          amount={data.igst_total || "0"}
          showPercentage
          percentage={data.igst_percentage || "0"}
        />
      )}

      <Divider />

      <PriceSummaryRow
        label={t("cart.total")}
        amount={data.total_amount || "0"}
        variant="bold"
      />
    </Container>
  );
};

export default CartSummary;
