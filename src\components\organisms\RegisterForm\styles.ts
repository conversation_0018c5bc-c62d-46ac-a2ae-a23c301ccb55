import { styled } from "@/utils/styled";
import { View, Text } from "react-native";

export const Container = styled(View)`
  padding: 24px;
  background-color: ${({ theme }) => theme.colors.card};
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
`;

export const WelcomeText = styled(Text)`
  font-size: 24px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
  margin-bottom: 8px;
`;

export const Subtitle = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  text-align: center;
  margin-bottom: 32px;
`;

export const Form = styled(View)`
  /* gap: 16px; */
`;

export const ButtonContainer = styled(View)`
  margin-top: 24px;
`;

export const LoginContainer = styled(View)`
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
  gap: 4px;
`;

export const LoginText = styled(Text)`
  color: ${({ theme }) => theme.colors.text};
  font-size: 14px;
`;

export const LoginLink = styled(Text)`
  color: ${({ theme }) => theme.colors.primary};
  font-size: 14px;
  font-weight: 600;
`;
