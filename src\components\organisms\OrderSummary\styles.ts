import { styled } from "@/utils/styled";
import { View, Text, TextInput, Pressable } from "react-native";

export const Container = styled(View)`
  flex: 1;
  padding: 20px;
`;

export const Header = styled(View)`
  margin-bottom: 24px;
`;

export const Title = styled(Text)`
  font-size: 24px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 8px;
`;

export const Subtitle = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  line-height: 22px;
`;

export const Section = styled(View)`
  background-color: ${({ theme }) => theme.colors.card};
  padding: 20px;
  border-radius: 16px;
  margin-bottom: 20px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.05;
  shadow-radius: 4px;
  elevation: 2;
`;

export const SectionHeader = styled(View)`
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
`;

export const SectionIcon = styled(View)`
  background-color: ${({ theme }) => theme.colors.primary + "20"};
  border-radius: 8px;
  padding: 8px;
  margin-right: 12px;
`;

export const SectionTitle = styled(Text)`
  font-size: 18px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
`;

export const AddressContainer = styled(View)`
  gap: 4px;
`;

export const AddressName = styled(Text)`
  font-size: 14px;
  font-weight: bold;
  color: ${({ theme }) => theme.colors.text};
`;

export const AddressText = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
  line-height: 20px;
`;

export const ItemsContainer = styled(View)`
  gap: 12px;
`;

export const ItemRow = styled(View)`
  flex-direction: row;
  align-items: center;
  padding-bottom: 12px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.divider};
`;

export const ItemDetails = styled(View)`
  flex: 1;
  margin-left: 12px;
`;

export const ItemName = styled(Text)`
  font-size: 14px;
  font-weight: bold;
  color: ${({ theme }) => theme.colors.text};
`;

export const ItemQuantity = styled(Text)`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.text};
`;

export const PriceContainer = styled(View)`
  gap: 8px;
`;

export const Divider = styled(View)`
  height: 1px;
  background-color: ${({ theme }) => theme.colors.divider};
  margin: 8px 0;
`;

export const TotalContainer = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
`;

export const TotalLabel = styled(Text)`
  font-size: 16px;
  font-weight: bold;
  color: ${({ theme }) => theme.colors.text};
`;

export const TotalAmount = styled(View)`
  flex-direction: row;
  align-items: center;
  gap: 8px;
`;

export const CouponContainer = styled(View)`
  flex-direction: row;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  margin-bottom: 8px;
`;

export const CouponInput = styled(TextInput)`
  flex: 1;
  height: 40px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.divider};
  border-radius: 8px;
  padding-horizontal: 12px;
  color: ${({ theme }) => theme.colors.text};
  font-size: 14px;
`;

export const CouponButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.primary};
  padding-horizontal: 16px;
  height: 40px;
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  opacity: ${({ disabled }) => (disabled ? 0.5 : 1)};
`;

export const CouponButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 14px;
  font-weight: 600;
`;

export const SavingsText = styled(Text)`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.success};
  margin-top: 4px;
  margin-bottom: 8px;
`;

export const PaymentContainer = styled(View)`
  gap: 12px;
`;

export const PaymentMethodContainer = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

export const PaymentMethodLabel = styled(Text)`
  font-size: 14px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
`;

export const PaymentMethodValue = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.primary};
  font-weight: 600;
`;

export const PaymentProofContainer = styled(View)`
  flex-direction: row;
  align-items: center;
  gap: 8px;
`;

export const PaymentProofStatus = styled(View)`
  flex-direction: row;
  align-items: center;
`;

export const PaymentProofStatusText = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
  font-weight: 500;
`;
