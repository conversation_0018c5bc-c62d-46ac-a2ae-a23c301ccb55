import { createAsyncThunk } from "@reduxjs/toolkit";
import { ApiResponse } from "../../types/api";
import api from "../../services/api";

import { withToastForError } from "../../utils/thunk";
import {
  CountryCodeListResponse,
  LoginOTPPayload,
  LoginOTPVerifyResponse,
  RegisterOTPPayload,
  StateListResponse,
  CityListResponse,
  UpdateUserPayload,
  LoginOTPVerifyThunkPayload,
  UserDetails,
  SettingsList,
} from "../../types/auth";

export const getCountryListAction = createAsyncThunk(
  "auth/getCountryList",
  withToastForError(async (): Promise<CountryCodeListResponse> => {
    const response = await api.get("/country-list");
    return response.data;
  })
);

export const registerOTPAction = createAsyncThunk(
  "auth/registerOTP",
  withToastForError(
    async (payload: RegisterOTPPayload): Promise<ApiResponse> => {
      const response = await api.post("/register-otp", payload);
      return response.data;
    }
  )
);

export const loginOTPAction = createAsyncThunk(
  "auth/loginOTP",
  withToastForError(async (payload: LoginOTPPayload): Promise<ApiResponse> => {
    const response = await api.post("/login-otp", payload);
    return response.data;
  })
);

export const loginOTPVerifyAction = createAsyncThunk(
  "auth/loginOTPVerify",
  withToastForError(
    async (
      data: LoginOTPVerifyThunkPayload
    ): Promise<LoginOTPVerifyResponse> => {
      const response = await api.post("/verify-otp", data.payload);
      return { ...response.data, is_sale_person: data.isSalePerson };
    }
  )
);

export const getStateListAction = createAsyncThunk(
  "auth/getStateList",
  withToastForError(async (country_id): Promise<StateListResponse> => {
    const response = await api.get(`/state-list`, {
      params: { country_id },
    });
    return response.data;
  })
);

export const getCityListAction = createAsyncThunk(
  "auth/getCityList",
  withToastForError(async (state_id): Promise<CityListResponse> => {
    const response = await api.get(`/city-list`, {
      params: { state_id },
    });
    return response.data;
  })
);

export const updateUserAction = createAsyncThunk(
  "auth/updateUser",
  withToastForError(
    async (payload: UpdateUserPayload): Promise<ApiResponse> => {
      const response = await api.post("/update-profile", payload);
      return response.data;
      // return sleep(2, {
      //   data: { ...payload },
      //   message: "User updated successfully",
      //   status: true,
      // }) as unknown as ApiResponse;
    }
  )
);

export const logoutAction = createAsyncThunk(
  "auth/logout",
  withToastForError(async (): Promise<ApiResponse> => {
    const response = await api.post("/logout");
    return response.data;
  })
);

export const logoutLocalAction = createAsyncThunk(
  "auth/logoutLocal",
  withToastForError(async (): Promise<ApiResponse> => {
    return {
      data: {},
      message: "Unauthorized access detected!",
      status: true,
    };
  })
);

export const getUserDetailsAction = createAsyncThunk(
  "auth/getUserDetails",
  withToastForError(async (): Promise<UserDetails & ApiResponse> => {
    const response = await api.get("/get-profile");
    return response.data;
  })
);

export const getSettingsListAction = createAsyncThunk(
  "auth/getSettingsList",
  withToastForError(async (): Promise<SettingsList & ApiResponse> => {
    const response = await api.get("/setting-list");
    return response.data;
  })
);
