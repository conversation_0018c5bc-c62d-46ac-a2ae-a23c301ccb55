import {styled} from "../utils/styled";

export const TabBarContainer = styled.View`
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  height: 60px;
  background-color: ${({ theme }) => theme.colors.background};
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.border};
`;

const OrderIconCircle = styled.View<{
  focused: boolean;
  size: number;
}>`
  width: ${({ size }) => size * 1.6}px;
  height: ${({ size }) => size * 1.6}px;
  border-radius: ${({ size }) => (size * 1.6) / 2}px;
  background-color: ${({ focused }) => (focused ? '#007bff' : 'transparent')};
  align-items: center;
  justify-content: center;
  margin-bottom: ${({ focused, size }) => (focused ? size * 0.3 : 0)}px;
`;

export { OrderIconCircle };