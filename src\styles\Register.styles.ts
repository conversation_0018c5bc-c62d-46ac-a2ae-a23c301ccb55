import { styled } from "../utils/styled";
import Button from "../components/atoms/Button";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { Image } from "react-native";

export const Container = styled.View`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const TopContainer = styled.View`
  flex: 1;
  align-items: center;
  justify-content: flex-end;
  min-height: 260px;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const BottomCard = styled.View`
  width: 100%;
  background-color: ${({ theme }) => theme.colors.white};
  border-top-left-radius: 32px;
  border-top-right-radius: 32px;
  padding-horizontal: 24px;
  padding-top: 40px;
  padding-bottom: 32px;
  align-items: center;
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px -2px;
  shadow-opacity: 0.06;
  shadow-radius: 8px;
  elevation: 8;
`;

export const WelcomeText = styled.Text`
  font-size: 24px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 12px;
  text-align: center;
`;

export const Subtitle = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  margin-bottom: 32px;
  text-align: center;
`;

export const Form = styled.View`
  width: 100%;
`;

export const Input = styled.TextInput`
  background-color: ${({ theme }) => theme.colors.inputBackground};
  border-radius: 12px;
  padding-horizontal: 16px;
  padding-vertical: 12px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 8px;
  width: 100%;
`;

export const InputRow = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.inputBackground};
  border-radius: 12px;
  margin-bottom: 8px;
  width: 100%;
  padding-horizontal: 8px;
  padding-vertical: 2px;
`;

export const CountryCodeButton = styled.Pressable`
  padding-horizontal: 12px;
  padding-vertical: 10px;
  border-right-width: 1px;
  border-right-color: ${({ theme }) => theme.colors.divider};
  justify-content: center;
  align-items: center;
  flex-direction: row;
`;

export const CountryCodeText = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  font-weight: 600;
`;

export const PhoneInput = styled.TextInput`
  flex: 1;
  padding-horizontal: 12px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  background-color: transparent;
  height: 48px;
`;

export const ErrorText = styled.Text`
  color: ${({ theme }) => theme.colors.error};
  font-size: 13px;
  margin-bottom: 10px;
  margin-left: 4px;
  align-self: flex-start;
`;

export const ContinueButton = styled(Button)`
  background-color: ${({ theme }) => theme.colors.secondary};
  border-radius: 12px;
  padding-vertical: 16px;
  align-items: center;
  width: 100%;
  margin-top: 8px;
`;

export const LoginContainer = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
`;

export const LoginText = styled.Text`
  color: ${({ theme }) => theme.colors.gray};
  font-size: 14px;
`;

export const LoginLink = styled.Pressable`
  align-items: center;
  padding-vertical: 12px;
`;

export const LoginLinkText = styled.Text`
  color: ${({ theme }) => theme.colors.link};
  font-weight: 600;
  font-size: 14px;
  margin-left: 4px;
`;

export const StyledKeyboardAwareScrollView = styled(KeyboardAwareScrollView)<{
  theme: any;
}>`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
  flex-grow: 1;
`;

export const StyledImage = styled(Image)`
  height: 80%;
  width: 100%;
`;
