import { styled } from "@/utils/styled";

export const Container = styled.View`
  background-color: ${({ theme }) => theme.colors.cardBackground};
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

export const Header = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
`;

export const Info = styled.View`
  flex: 1;
  margin-right: 16px;
`;

export const Name = styled.Text`
  font-size: 20px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 6px;
  line-height: 24px;
`;

export const Email = styled.Text`
  font-size: 15px;
  color: ${({ theme }) => theme.colors.gray};
  margin-bottom: 4px;
  line-height: 18px;
`;

export const Phone = styled.Text`
  font-size: 15px;
  color: ${({ theme }) => theme.colors.textSecondary};
  line-height: 18px;
`;

export const Actions = styled.View`
  flex-direction: row;
  gap: 10px;
`;

export const ActionButton = styled.Pressable<{
  variant?: "edit" | "delete";
}>`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: ${({ theme, variant }) =>
    variant === "delete"
      ? theme.colors.errorBackground
      : theme.colors.primaryBackground};
  align-items: center;
  justify-content: center;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

export const Details = styled.View`
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.border};
  padding-top: 16px;
`;

export const DetailRow = styled.View`
  flex-direction: row;
  margin-bottom: 10px;
  align-items: flex-start;
`;

export const DetailLabel = styled.Text`
  font-size: 14px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  width: 100px;
  margin-right: 12px;
  line-height: 18px;
`;

export const DetailValue = styled.Text`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  flex: 1;
  line-height: 18px;
`;

export const StatusBadge = styled.View<{
  status: "authorized" | "unauthorized";
}>`
  padding-vertical: 4px;
  border-radius: 12px;
  background-color: ${({ theme, status }) =>
    status === "authorized"
      ? theme.colors.successBackground
      : theme.colors.warningBackground};
  align-self: flex-start;
  margin-top: 4px;
`;

export const StatusText = styled.Text<{
  status: "authorized" | "unauthorized";
}>`
  font-size: 12px;
  font-weight: 600;
  color: ${({ theme, status }) =>
    status === "authorized" ? theme.colors.success : theme.colors.warning};
`;

export const EmptyStateContainer = styled.View`
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
`;

export const EmptyStateIcon = styled.View`
  margin-bottom: 16px;
  opacity: 0.6;
`;

export const EmptyStateText = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.textSecondary};
  text-align: center;
  line-height: 22px;
  margin-bottom: 8px;
`;

export const EmptyStateSubtext = styled.Text`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textTertiary};
  text-align: center;
  line-height: 20px;
`;
