import { styled } from "@/utils/styled";
import { BottomSheetModal } from "@gorhom/bottom-sheet";

export const StyledBottomSheetModal = styled(BottomSheetModal)`
  flex: 1;
`;

export const StyledBottomSheetView = styled.View`
  flex: 1;
`;

export const HeaderRow = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
`;

export const HeaderTitle = styled.Text`
  font-weight: 600;
  font-size: 18px;
  color: ${({ theme }) => theme.colors.text};
`;

export const CloseButton = styled.Pressable`
  padding: 4px;
`;

export const Divider = styled.View`
  height: 1px;
  background-color: ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.gray};
  margin-bottom: 8px;
`;

export const SectionTitle = styled.Text`
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 8px;
  color: ${({ theme }) => theme.colors.text};
`;

export const FilterContainer = styled.View`
  margin-bottom: 12px;
  padding-horizontal: 16px;
  flex-wrap: wrap;
  flex-direction: row;
  gap: 8px;
`;

export const FilterChip = styled.Pressable<{ active?: boolean }>`
  padding: 8px 16px;
  border-radius: 20px;
  margin-right: 8px;
  background-color: ${({ theme, active }) =>
    active ? theme.colors.primary : theme.colors.card};
  width: fit-content;
`;

export const FilterChipText = styled.Text<{ active?: boolean }>`
  color: ${({ theme, active }) =>
    active ? theme.colors.white : theme.colors.text};
  font-size: 14px;
`;

export const FilterOption = styled.Pressable<{ selected: boolean }>`
  flex-direction: row;
  align-items: center;
  padding: 12px;
  background-color: ${({ selected, theme }) =>
    selected ? theme.colors.primary : theme.colors.gray};
  border-radius: 8px;
  margin-bottom: 8px;
`;

export const OptionText = styled.Text<{ selected: boolean }>`
  font-size: 14px;
  margin-left: 8px;
  color: ${({ selected, theme }) =>
    selected ? theme.colors.white : theme.colors.text};
`;

export const ButtonRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background};
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.border};
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  gap: 16px;
`;

export const ClearButton = styled.Pressable`
  padding: 12px 24px;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.white};
  text-align: center;
  justify-content: center;
`;

export const ClearText = styled.Text`
  font-weight: bold;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.primary};
  text-align: center;
  justify-content: center;
`;

export const ApplyButton = styled.Pressable`
  padding: 12px 24px;
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 8px;
  flex: 1;
  text-align: center;
  justify-content: center;
`;

export const ApplyText = styled.Text`
  color: ${({ theme }) => theme.colors.white};
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  justify-content: center;
`;


