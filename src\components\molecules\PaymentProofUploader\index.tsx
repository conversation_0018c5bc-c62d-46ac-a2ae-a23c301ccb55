import React, { useState } from "react";
import { ActivityIndicator } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import {
  PaymentProofContainer,
  PaymentProofLabel,
  PaymentProofButton,
  PaymentProofButtonText,
  PaymentProofPreview,
  PaymentProofPreviewImage,
  PaymentProofRemoveButton,
  PaymentProofRemoveButtonText,
  PaymentProofInfo,
  PaymentProofInfoText,
} from "./styles";
import { useTheme } from "@/hooks/useTheme";
import Toast from "react-native-toast-message";
import ImagePickerModal from "../ImagePickerModal";
import { useFileValidation } from "@/hooks/useFileValidation";

interface PaymentProofUploaderProps {
  paymentProof: ImagePicker.ImagePickerAsset | null;
  onUpload: (asset: ImagePicker.ImagePickerAsset) => void;
  onRemove: () => void;
  required?: boolean;
}

const PaymentProofUploader: React.FC<PaymentProofUploaderProps> = ({
  paymentProof,
  onUpload,
  onRemove,
  required = false,
}) => {
  const { theme } = useTheme();
  const { getSupportedFormatsDisplay, getMaxFileSize, validateImageFile } =
    useFileValidation();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const [loadingType, setLoadingType] = useState<'camera' | 'gallery' | null>(null);

  const validateAndUpload = async (asset: ImagePicker.ImagePickerAsset) => {
    setIsUploading(true);
    try {
      console.log("🔍 PaymentProofUploader - Starting validation for:", asset);
      const [validation] = await Promise.all([
        validateImageFile(asset),
        new Promise(resolve => setTimeout(resolve, 500))
      ]);

      console.log("🔍 PaymentProofUploader - Validation result:", validation);

      if (validation.isValid) {
        console.log(
          "✅ PaymentProofUploader - Validation passed, uploading..."
        );
        onUpload(asset);
        Toast.show({
          type: "success",
          text1: "Payment proof uploaded successfully!",
        });
        setIsModalVisible(false);
      } else {
        console.log(
          "❌ PaymentProofUploader - Validation failed:",
          validation.error
        );
        Toast.show({
          type: "error",
          text1: validation.error || "File validation failed",
        });
      }
    } catch (error) {
      console.error("❌ PaymentProofUploader - Validation error:", error);
      Toast.show({
        type: "error",
        text1: "Failed to validate file",
      });
    } finally {
      setIsUploading(false);
      setLoadingType(null);
    }
  };

  const handleCameraPress = async () => {
    try {
      setLoadingType('camera');
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== "granted") {
        Toast.show({
          type: "error",
          text1: "Camera permission is required!",
        });
        setLoadingType(null);
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await validateAndUpload(result.assets[0]);
      } else {
        setLoadingType(null);
      }
    } catch (error) {
      console.error("Camera error:", error);
      Toast.show({
        type: "error",
        text1: "Failed to capture image",
      });
      setLoadingType(null);
    }
  };

  const handleGalleryPress = async () => {
    try {
      setLoadingType('gallery');
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== "granted") {
        Toast.show({
          type: "error",
          text1: "Permission to access camera roll is required!",
        });
        setLoadingType(null);
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: false,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await validateAndUpload(result.assets[0]);
      } else {
        setLoadingType(null);
      }
    } catch (error) {
      console.error("Gallery error:", error);
      Toast.show({
        type: "error",
        text1: "Failed to select image",
      });
      setLoadingType(null);
    }
  };

  const handleUploadPaymentProof = () => {
    setIsModalVisible(true);
  };

  const handleRemove = async () => {
    setIsRemoving(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      onRemove();
      Toast.show({
        type: "info",
        text1: "Payment proof removed",
      });
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "Failed to remove image",
      });
    } finally {
      setIsRemoving(false);
    }
  };

  return (
    <>
      <PaymentProofContainer>
        <PaymentProofLabel>
          Payment Proof {required && "(Required)"}
        </PaymentProofLabel>

        {!paymentProof ? (
          <PaymentProofButton
            onPress={handleUploadPaymentProof}
            disabled={isUploading}
            style={{ opacity: isUploading ? 0.6 : 1 }}
          >
            {isUploading ? (
              <ActivityIndicator
                size={24}
                color={theme.colors.primary}
              />
            ) : (
              <Ionicons
                name="camera-outline"
                size={24}
                color={theme.colors.primary}
              />
            )}
            <PaymentProofButtonText>
              {isUploading ? "Uploading..." : "Upload Payment Screenshot"}
            </PaymentProofButtonText>
          </PaymentProofButton>
        ) : (
          <PaymentProofPreview>
            <PaymentProofPreviewImage
              source={{ uri: paymentProof.uri }}
              resizeMode="cover"
            />
            <PaymentProofRemoveButton
              onPress={handleRemove}
              disabled={isRemoving}
              style={{ opacity: isRemoving ? 0.6 : 1 }}
            >
              {isRemoving ? (
                <ActivityIndicator
                  size={16}
                  color={theme.colors.white}
                />
              ) : (
                <Ionicons
                  name="trash-outline"
                  size={16}
                  color={theme.colors.white}
                />
              )}
              <PaymentProofRemoveButtonText>
                {isRemoving ? "Removing..." : "Remove"}
              </PaymentProofRemoveButtonText>
            </PaymentProofRemoveButton>
          </PaymentProofPreview>
        )}

        {/* File requirements info */}
        <PaymentProofInfo>
          <PaymentProofInfoText>
            📁 Supported: {getSupportedFormatsDisplay("image")}
          </PaymentProofInfoText>
          <PaymentProofInfoText>
            📏 Max size: {getMaxFileSize("image")}KB
          </PaymentProofInfoText>
        </PaymentProofInfo>
      </PaymentProofContainer>

      <ImagePickerModal
        visible={isModalVisible}
        onClose={() => !isUploading && setIsModalVisible(false)}
        onCameraPress={handleCameraPress}
        onGalleryPress={handleGalleryPress}
        title="Upload Payment Proof"
        isLoading={isUploading}
        loadingType={loadingType}
      />
    </>
  );
};

export default PaymentProofUploader;
