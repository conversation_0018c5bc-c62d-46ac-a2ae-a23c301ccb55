import { styled } from "../utils/styled";

export const ButtonContainer = styled.Pressable<{
  variant: "primary" | "secondary" | "outline";
}>`
  padding: 12px 24px;
  border-radius: 8px;
  background-color: ${({ theme, variant }) =>
    variant === "primary"
      ? theme.colors.primary
      : variant === "secondary"
      ? theme.colors.secondary
      : "transparent"};
  border: ${({ theme, variant }) =>
    variant === "outline" ? `1px solid ${theme.colors.primary}` : "none"};
`;

export const ButtonContent = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: center;
`;

export const ButtonText = styled.Text<{
  variant: "primary" | "secondary" | "outline";
}>`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme, variant }) =>
    variant === "outline" ? theme.colors.primary : theme.colors.white};
  text-align: center;
`;

export const DisabledButton = styled(ButtonContainer)`
  background-color: ${({ theme }) => theme.colors.gray};
  opacity: 0.7;
`;
