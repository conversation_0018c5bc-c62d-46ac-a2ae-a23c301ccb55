import { styled } from "@/utils/styled";
import { View, Text } from "react-native";

export const Container = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  min-height: 20px;
`;

export const LabelContainer = styled(View)`
  flex: 1;
  margin-right: 16px;
`;

export const ValueContainer = styled(View)`
  flex: 1.5;
  align-items: flex-end;
`;

export const Label = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary || theme.colors.gray};
  font-weight: 500;
  line-height: 20px;
`;

export const Value = styled(Text)<{ variant?: "default" | "bold" | "accent" }>`
  font-size: 16px;
  color: ${({ theme, variant }) => {
    switch (variant) {
      case "bold":
        return theme.colors.text;
      case "accent":
        return theme.colors.primary;
      default:
        return theme.colors.text;
    }
  }};
  font-weight: ${({ variant }) => {
    switch (variant) {
      case "bold":
        return "600";
      case "accent":
        return "600";
      default:
        return "400";
    }
  }};
  line-height: 20px;
  text-align: right;
`;
