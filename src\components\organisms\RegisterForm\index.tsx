import React, { useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { Pressable } from "react-native";
import { useTranslation } from "react-i18next";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { getCountryListAction } from "@/store/actions/auth";
import { CountryCode } from "@/types/auth";
import FormField from "../../molecules/FormField";
import PhoneInput from "../../molecules/PhoneInput";
import Button from "../../atoms/Button";
import Text from "../../atoms/Text";
import {
  Container,
  WelcomeText,
  Subtitle,
  Form,
  LoginContainer,
  LoginText,
  LoginLink,
} from "./styles";
import { useTheme } from "@/hooks/useTheme";

interface RegisterFormProps {
  onSubmit: () => void;
  onLoginPress: () => void;
  loading?: boolean;
}

export interface RegisterFormData {
  firstName: string;
  lastName: string;
  mobileNumber: string;
}

const RegisterForm: React.FC<RegisterFormProps> = ({
  onSubmit,
  onLoginPress,
  loading = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const { countryList } = useAppSelector((state) => state.auth);
  const {
    formState: { isValid },
  } = useFormContext();

  const [countryCode, setCountryCode] = useState<CountryCode | null>(
    countryList?.[0] || null
  );

  useEffect(() => {
    const fetchCountryList = async () => {
      if (!countryList) {
        const response = await dispatch(getCountryListAction({})).unwrap();
        if (response.status && response?.data) {
          setCountryCode(response?.data?.[0]);
        }
      }
    };
    fetchCountryList();
  }, [dispatch, countryList]);

  const handleCountrySelect = (country: CountryCode) => {
    setCountryCode(country);
  };

  return (
    <Container>
      <WelcomeText>
        {t("create_account")} <Text style={{ fontSize: 22 }}>👋</Text>
      </WelcomeText>
      <Subtitle>{t("fill_details")}</Subtitle>

      <Form>
        <FormField
          name="firstName"
          placeholder={t("first_name")}
          placeholderTextColor={theme.colors.gray}
          label={t("first_name")}
        />

        <FormField
          name="lastName"
          placeholder={t("last_name")}
          placeholderTextColor={theme.colors.gray}
          label={t("last_name")}
        />

        <PhoneInput
          label={t("phone_number")}
          name="mobileNumber"
          placeholder={t("phone_number")}
          countryCode={countryCode}
          onCountrySelect={handleCountrySelect}
          countryList={countryList || []}
        />

        <Button
          title={t("continue")}
          onPress={onSubmit}
          loading={loading}
          disabled={!isValid || loading}
        />

        <LoginContainer>
          <LoginText>{t("already_have_account")}</LoginText>
          <Pressable onPress={onLoginPress}>
            <LoginLink>{t("login")}</LoginLink>
          </Pressable>
        </LoginContainer>
      </Form>
    </Container>
  );
};

export default RegisterForm;
