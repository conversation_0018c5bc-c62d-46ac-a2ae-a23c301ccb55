import { styled } from "@/utils/styled";
import { Pressable, View, Text } from "react-native";

export const CheckboxContainer = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  padding: 8px 0;
`;

export const CheckboxCircle = styled(View)<{
  checked: boolean;
  disabled: boolean;
}>`
  width: 24px;
  height: 24px;
  border-radius: 12px;
  border-width: 2px;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
  opacity: ${({ disabled }) => (disabled ? 0.5 : 1)};
`;

export const CheckboxLabel = styled(Text)<{
  checked: boolean;
  disabled: boolean;
}>`
  font-size: 16px;
  font-weight: ${({ checked }) => (checked ? "600" : "400")};
  color: ${({ theme, disabled }) =>
    disabled ? theme.colors.textSecondary : theme.colors.text};
  flex: 1;
`;
