import React from "react";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { ProductImage, PriceText } from "@/components/atoms";
import QuantitySelector from "../QuantitySelector";
import { useTheme } from "@/hooks/useTheme";
import {
  Container,
  ItemDetails,
  ItemName,
  RemoveButton,
  OutOfStockOverlay,
  OutOfStockText,
} from "./styles";

interface CartItem {
  id: number;
  product_id: number;
  product: {
    title: string;
    main_image?: {
      url: string;
    };
  };
  quantity: number;
  price: string;
  availablity?: 0 | 1; // 0 = out of stock, 1 = available
}

interface CartItemCardProps {
  item: CartItem;
  onQuantityChange: (productId: number, newQuantity: number) => void;
  onRemove: (itemId: number) => void;
  onPress?: () => void;
}

/**
 * CartItemCard - Molecule component for individual cart items
 * Displays product info, quantity controls, and remove option
 * Shows disabled state when item is out of stock
 */
const CartItemCard: React.FC<CartItemCardProps> = ({
  item,
  onQuantityChange,
  onRemove,
  onPress,
}) => {
  const { theme } = useTheme();
  const { t } = useTranslation();

  if (!item) {
    return null;
  }

  const isOutOfStock = item.availablity === 0;

  const handleIncrement = () => {
    if (isOutOfStock) return;
    onQuantityChange(item.product_id, item.quantity + 1);
  };

  const handleDecrement = () => {
    if (isOutOfStock) return;
    onQuantityChange(item.product_id, item.quantity - 1);
  };

  const handleRemove = () => {
    // Remove functionality is preserved even for out-of-stock items
    // so users can clean up their cart
    onRemove(item.product_id);
  };

  const handlePress = () => {
    if (isOutOfStock) return;
    onPress?.();
  };

  return (
    <Container onPress={handlePress} disabled={isOutOfStock}>
      <ProductImage uri={item.product.main_image?.url} size="medium" />

      <ItemDetails>
        <ItemName>{item.product.title}</ItemName>
        <PriceText amount={item.price} variant="primary" size="medium" />

        <QuantitySelector
          quantity={item.quantity}
          onIncrement={handleIncrement}
          onDecrement={handleDecrement}
          minQuantity={1}
          disabled={isOutOfStock}
        />
      </ItemDetails>

      <RemoveButton onPress={handleRemove} isOutOfStock={isOutOfStock}>
        <Ionicons name="trash-outline" size={24} color={theme.colors.error} />
      </RemoveButton>

      {isOutOfStock && (
        <OutOfStockOverlay>
          <OutOfStockText>
            {t("product.out_of_stock")} - {t("common.not_available")}
          </OutOfStockText>
        </OutOfStockOverlay>
      )}
    </Container>
  );
};

export default CartItemCard;
