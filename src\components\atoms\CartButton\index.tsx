import React from 'react';
import { TouchableOpacityProps, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/hooks/useTheme';
import { StyledButton, ButtonText } from './styles';

interface CartButtonProps extends TouchableOpacityProps {
  title: string;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  icon?: keyof typeof Ionicons.glyphMap;
  fullWidth?: boolean;
}

/**
 * CartButton - Atomic component for cart action buttons
 * Supports different variants, sizes, and loading states
 */
const CartButton: React.FC<CartButtonProps> = ({
  title,
  variant = 'primary',
  size = 'medium',
  loading = false,
  icon,
  fullWidth = false,
  disabled,
  ...props
}) => {
  const { theme } = useTheme();

  return (
    <StyledButton
      variant={variant}
      size={size}
      fullWidth={fullWidth}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? (
        <ActivityIndicator 
          size="small" 
          color={variant === 'secondary' ? theme.colors.text : theme.colors.white} 
        />
      ) : (
        <>
          {icon && (
            <Ionicons
              name={icon}
              size={size === 'small' ? 16 : size === 'large' ? 24 : 20}
              color={variant === 'secondary' ? theme.colors.text : theme.colors.white}
              style={{ marginRight: 8 }}
            />
          )}
          <ButtonText variant={variant} size={size}>
            {title}
          </ButtonText>
        </>
      )}
    </StyledButton>
  );
};

export default CartButton;
