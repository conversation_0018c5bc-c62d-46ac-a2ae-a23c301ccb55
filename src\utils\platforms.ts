import { Platform, Dimensions } from "react-native";
const { width } = Dimensions.get("window");

export const isIOS = Platform.OS === "ios";
export const isAndroid = Platform.OS === "android";
export const isNative = isIOS || isAndroid;
export const isWeb = !isNative;
export const isTablet = isNative
  ? isIOS && width >= 768
  : isWeb &&
    global.window.matchMedia("only screen and (min-width: 768px)")?.matches;

export const isMobileWeb =
  isWeb &&
  global.window.matchMedia("only screen and (max-width: 1000px)")?.matches;
export const isDesktopWeb = isWeb && !isMobileWeb;
