import { styled } from "@/utils/styled";
import { View, Text, Pressable, Image } from "react-native";

export const FileUploaderContainer = styled(View)`
  margin-bottom: 16px;
`;

export const FileUploaderLabel = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 8px;
`;

export const FileUploaderButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.card};
  border: 2px dashed ${({ theme }) => theme.colors.border};
  border-radius: 12px;
  min-height: 80px;
`;

export const FileUploaderButtonText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.primary};
  margin-left: 8px;
  font-weight: 500;
`;

export const FileUploaderPreview = styled(View)`
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 12px;
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

export const FileUploaderPreviewImage = styled(Image)`
  width: 100%;
  height: 200px;
  border-radius: 8px;
  margin-bottom: 12px;
`;

export const FileUploaderRemoveButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: ${({ theme }) => theme.colors.error};
  padding: 8px 16px;
  border-radius: 8px;
  margin-top: 8px;
`;

export const FileUploaderRemoveButtonText = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.white};
  margin-left: 4px;
  font-weight: 500;
`;

export const FileInfoText = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary};
  text-align: center;
`;
