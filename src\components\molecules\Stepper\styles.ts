import { FlatList, PixelRatio, Pressable, Text, View } from "react-native";
import { styled } from "@/utils/styled";

export const StepInfoContainer = styled(Pressable)`
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  flex: 1;
`;
export const StepInfo = styled(View)<{ width?: number }>`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: ${({ width }) => width || 75}px;
`;

export const StepList = styled(FlatList).attrs({
  contentContainerStyle: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 10,
    columnGap: 10,
    flexGrow: 1,
  },
})`
  display: flex;
`;
export const StepText = styled(Text)<{ isCurrentStep: boolean }>`
  font-size: 14px;
  color: ${({ theme, isCurrentStep }) =>
    !isCurrentStep ? theme.colors.text : theme.colors.primary};
  text-align: center;
`;
export const Separator = styled(View)<{ width: number }>`
  width: ${({ width }) => width || 55}px;
  height: 3px;
  background-color: ${({ theme }) => theme.colors.lightGray};
  margin-top: auto;
  margin-bottom: auto;
`;
