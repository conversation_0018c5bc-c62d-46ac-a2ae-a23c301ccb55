import React from "react";
import { FlatList, ListRenderItem } from "react-native";
import { CartItemCard } from "@/components/molecules";
import { Container } from "./styles";

interface CartItem {
  id: number;
  product: {
    id: number;
    title: string;
    main_image?: {
      url: string;
    };
  };
  quantity: number;
  price: string;
}

interface CartItemListProps {
  items: CartItem[];
  onQuantityChange: (productId: number, newQuantity: number) => void;
  onRemoveItem: (itemId: number) => void;
}

const CartItemList: React.FC<CartItemListProps> = ({
  items,
  onQuantityChange,
  onRemoveItem,
}) => {
  const renderItem: ListRenderItem<CartItem> = ({ item }) => {
    return (
      <CartItemCard
        item={item}
        onQuantityChange={onQuantityChange}
        onRemove={onRemoveItem}
      />
    );
  };
  const keyExtractor = (item: CartItem) => item.id.toString();

  return (
    <Container>
      <FlatList
        data={items}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingHorizontal: 16,
          paddingBottom: 20,
        }}
      />
    </Container>
  );
};

export default CartItemList;
