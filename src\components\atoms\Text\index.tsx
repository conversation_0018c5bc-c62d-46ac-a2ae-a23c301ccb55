import React from 'react';
import { TextProps } from 'react-native';
import { StyledText } from './styles';

interface CustomTextProps extends TextProps {
  variant?: 'body' | 'title' | 'subtitle' | 'caption' | 'error';
  color?: string;
}

const Text: React.FC<CustomTextProps> = ({ 
  variant = 'body', 
  color,
  children,
  ...props 
}) => {
  return (
    <StyledText variant={variant} color={color} {...props}>
      {children}
    </StyledText>
  );
};

export default Text;
