import { styled } from "../utils/styled";
import { View, Text, Pressable, FlatList } from "react-native";
import { Image } from "expo-image";

export const Container = styled(View)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const CartList = styled(FlatList)`
  flex: 1;
  padding: 16px;
`;

export const CartItem = styled(View)`
  flex-direction: row;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 12px;
  background-color: ${({ theme }) => theme.colors.card};
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
`;

export const ItemImage = styled(Image)`
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin-right: 16px;
`;

export const ItemDetails = styled(View)`
  flex: 1;
  justify-content: space-between;
`;

export const ItemName = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
`;

export const ItemPrice = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.primary};
  margin-bottom: 8px;
`;

export const QuantityContainer = styled(View)`
  flex-direction: row;
  align-items: center;
`;

export const QuantityButton = styled(Pressable)`
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: ${({ theme }) => theme.colors.primary};
  justify-content: center;
  align-items: center;
`;

export const Quantity = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  margin-horizontal: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const RemoveButton = styled(Pressable)`
  padding: 8px;
`;

export const Footer = styled(View)`
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.card};
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.border};
  justify-content: flex-end;
`;

export const TotalContainer = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

export const TotalLabel = styled(Text)`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
`;

export const TotalAmount = styled(Text)`
  font-size: 24px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.primary};
`;

export const ButtonContainer = styled(View)`
  flex-direction: row;
  justify-content: space-between;
`;

export const ClearButton = styled(Pressable)`
  flex: 1;
  padding: 16px;
  border-radius: 8px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.error};
  margin-right: 8px;
  align-items: center;
`;

export const ClearButtonText = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.error};
`;

export const CheckoutButton = styled(Pressable)`
  flex: 2;
  padding: 16px;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.primary};
  align-items: center;
`;

export const CheckoutButtonText = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.white};
`;

export const EmptyContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 32px;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const EmptyText = styled(Text)`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-top: 16px;
  margin-bottom: 24px;
  text-align: center;
`;

export const ShopButton = styled(Pressable)`
  padding: 16px 32px;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.primary};
`;

export const ShopButtonText = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.white};
`;

export const StepperContainer = styled(View)`
  padding: 16px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.border};
`;

export const ContentContainer = styled(View)`
  flex: 1;
`;
