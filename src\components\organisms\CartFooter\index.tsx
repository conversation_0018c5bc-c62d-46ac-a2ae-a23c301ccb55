import React from "react";
import { useTranslation } from "react-i18next";
import { CartButton } from "@/components/atoms";
import { Container, ButtonContainer } from "./styles";

interface CartFooterProps {
  onClearCart: () => void;
  onContinue: () => void;
  clearButtonTitle?: string;
  continueButtonTitle?: string;
  loading?: boolean;
  disabled?: boolean;
  clearButtonVariant?: "primary" | "secondary" | "danger";
}

/**
 * CartFooter - Organism component for cart action buttons
 * Contains clear cart and continue/checkout buttons
 */
const CartFooter: React.FC<CartFooterProps> = ({
  onClearCart,
  onContinue,
  clearButtonTitle,
  clearButtonVariant = "secondary",
  continueButtonTitle,
  loading = false,
  disabled = false,
}) => {
  const { t } = useTranslation();
  return (
    <Container>
      <ButtonContainer>
        <CartButton
          title={clearButtonTitle || t("cart.clear")}
          variant={clearButtonVariant}
          onPress={onClearCart}
          icon={clearButtonVariant === "danger" ? "trash-outline" : undefined}
          disabled={loading}
        />

        <CartButton
          title={continueButtonTitle || t("continue")}
          variant="primary"
          onPress={onContinue}
          loading={loading}
          disabled={disabled}
          fullWidth
          icon="arrow-forward"
        />
      </ButtonContainer>
    </Container>
  );
};

export default CartFooter;
