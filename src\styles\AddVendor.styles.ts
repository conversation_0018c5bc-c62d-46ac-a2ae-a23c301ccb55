import { styled } from "../utils/styled";
import Button from "../components/atoms/Button";
import { Pressable, Text } from "react-native";

export const Container = styled.View`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const ContentContainer = styled.View`
  width: 100%;
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
  padding: 24px;
  align-items: center;
`;

export const BottomContainer = styled.View`
  margin-top: auto;
  align-items: center;
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
`;

export const WelcomeText = styled.Text`
  font-size: 24px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 12px;
  text-align: center;
`;

export const Subtitle = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  margin-bottom: 32px;
  text-align: center;
`;

export const Form = styled.View`
  width: 100%;
  flex: 1;
`;

export const StepperContainer = styled.View`
  width: 100%;
  margin-bottom: 24px;
`;

export const Input = styled.TextInput`
  background-color: ${({ theme }) => theme.colors.inputBackground};
  border-radius: 12px;
  padding-horizontal: 16px;
  padding-vertical: 12px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 8px;
  width: 100%;
`;

export const InputRow = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.inputBackground};
  border-radius: 12px;
  margin-bottom: 8px;
  width: 100%;
  padding-horizontal: 8px;
  padding-vertical: 2px;
`;

export const CountryCodeButton = styled.Pressable`
  padding-horizontal: 12px;
  padding-vertical: 10px;
  border-right-width: 1px;
  border-right-color: ${({ theme }) => theme.colors.divider};
  justify-content: center;
  align-items: center;
  flex-direction: row;
`;

export const CountryCodeText = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  font-weight: 600;
`;

export const PhoneInput = styled.TextInput`
  flex: 1;
  padding-horizontal: 12px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  background-color: transparent;
  height: 48px;
`;

export const ErrorText = styled.Text`
  color: ${({ theme }) => theme.colors.error};
  font-size: 13px;
  margin-bottom: 10px;
  margin-left: 4px;
  align-self: flex-start;
`;

export const ContinueButton = styled(Button)`
  background-color: ${({ theme }) => theme.colors.secondary};
  border-radius: 20px;
  padding-vertical: 16px;
  align-items: center;
  margin-top: 8px;
  flex: 1;
`;

export const BackButton = styled.Pressable`
  margin-top: 16px;
  border-width: 1px;
  flex: 1;
  align-items: center;
  justify-content: center;
  border-color: ${({ theme }) => theme.colors.lightGray};
  border-radius: 12px;
  padding-vertical: 12px;
  margin-top: 8px;
  border-radius: 20px;
`;

export const BackButtonText = styled.Text`
  color: ${({ theme }) => theme.colors.black};
  font-size: 14px;
`;

export const PickerButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.background};
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 8px;
`;

export const PickerText = styled(Text)`
  color: ${({ theme }) => theme.colors.text};
  font-size: 16px;
`;

export const DisabledPickerButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.disabled};
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 8px;
  opacity: 0.5;
`;
