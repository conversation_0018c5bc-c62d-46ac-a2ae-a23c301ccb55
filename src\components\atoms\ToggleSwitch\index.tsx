import React, { useEffect, useRef } from "react";
import { Animated, Pressable } from "react-native";
import { useTheme } from "@/hooks/useTheme";
import {
  Container,
  Track,
  Thumb,
  Label,
  SwitchContainer,
} from "./styles";

interface ToggleSwitchProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
  label?: string;
  disabled?: boolean;
  size?: "small" | "medium" | "large";
  testID?: string;
}

const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
  value,
  onValueChange,
  label,
  disabled = false,
  size = "medium",
  testID,
}) => {
  const { theme } = useTheme();
  const animatedValue = useRef(new Animated.Value(value ? 1 : 0)).current;

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: value ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [value, animatedValue]);

  const handlePress = () => {
    if (!disabled) {
      onValueChange(!value);
    }
  };

  const trackColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.gray, theme.colors.primary],
  });

  const thumbTranslateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [2, size === "small" ? 18 : size === "large" ? 30 : 24],
  });

  const thumbScale = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 1.1],
  });

  return (
    <Container>
      {label && <Label>{label}</Label>}
      <Pressable
        onPress={handlePress}
        disabled={disabled}
        testID={testID}
        accessibilityRole="switch"
        accessibilityState={{ checked: value }}
        accessibilityLabel={label}
      >
        <SwitchContainer size={size} disabled={disabled}>
          <Track
            as={Animated.View}
            style={{ backgroundColor: trackColor }}
            size={size}
            disabled={disabled}
          >
            <Thumb
              as={Animated.View}
              style={{
                transform: [
                  { translateX: thumbTranslateX },
                  { scale: thumbScale },
                ],
              }}
              size={size}
              active={value}
            />
          </Track>
        </SwitchContainer>
      </Pressable>
    </Container>
  );
};

export default ToggleSwitch;
