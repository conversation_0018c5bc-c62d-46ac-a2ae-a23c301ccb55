{"name": "ozone-batteries", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start --clear", "start:clean": "npx expo start --clear", "android": "npx expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "rm:nm": "yarn cache clean && npm cache verify && npm cache clean --force && rm -rf node_modules && rm -rf package-lock.json && rm -rf yarn.lock && rm -rf android && rm -rf ios", "ios:clean": "cd ios && pod deintegrate && rm -rf build && rm -rf Podfile.lock && pod install && cd ..", "android:clean": "cd android && ./gradlew clean && ./gradlew", "android:build": "cd android && ./gradlew clean && ./gradlew && ./gradlew bundleRelease && ./gradlew installRelease", "web:build": "npx expo export --platform web"}, "dependencies": {"@gorhom/bottom-sheet": "^5.1.6", "@hookform/resolvers": "^5.0.1", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/slider": "^4.5.7", "@react-navigation/material-top-tabs": "^7.2.13", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.9.0", "expo": "~52.0.46", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.8", "expo-file-system": "~18.0.12", "expo-image": "~2.0.7", "expo-image-picker": "~16.0.6", "expo-linking": "~7.0.5", "expo-media-library": "~17.0.6", "expo-modules-core": "~2.2.3", "expo-notifications": "~0.29.14", "expo-router": "~4.0.21", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-web-browser": "~14.0.2", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "lodash": "^4.17.21", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.2", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-pager-view": "^6.8.0", "react-native-reanimated": "~3.16.1", "react-native-reanimated-carousel": "^4.0.2", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-tab-view": "^4.1.0", "react-native-toast-message": "^2.3.0", "react-native-web": "~0.19.13", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "styled-components": "^6.1.18", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.27.1", "@types/react": "~18.3.12", "@types/styled-components": "^5.1.34", "@types/styled-components-react-native": "^5.2.5", "babel-plugin-module-resolver": "^5.0.2", "typescript": "^5.8.3"}, "private": true}