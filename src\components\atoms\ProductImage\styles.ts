import { styled } from "@/utils/styled";
import { Image, View } from "react-native";

interface ImageProps {
  size: 'small' | 'medium' | 'large';
  borderRadius: number;
}

const getImageSize = (size: 'small' | 'medium' | 'large') => {
  switch (size) {
    case 'small':
      return '50px';
    case 'large':
      return '100px';
    default:
      return '80px';
  }
};

export const StyledImage = styled(Image)<ImageProps>`
  width: ${({ size }) => getImageSize(size)};
  height: ${({ size }) => getImageSize(size)};
  border-radius: ${({ borderRadius }) => borderRadius}px;
  background-color: ${({ theme }) => theme.colors.card};
`;

export const PlaceholderContainer = styled(View)<ImageProps>`
  width: ${({ size }) => getImageSize(size)};
  height: ${({ size }) => getImageSize(size)};
  border-radius: ${({ borderRadius }) => borderRadius}px;
  background-color: ${({ theme }) => theme.colors.card};
  align-items: center;
  justify-content: center;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;
