import { styled } from "@/utils/styled";
import { View, Text, Pressable } from "react-native";

interface ButtonTextProps {
  variant: "cancel" | "confirm";
}

export const ModalOverlay = styled(View)`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

export const ModalContent = styled(View)`
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 16px;
  padding: 24px;
  width: 100%;
  max-width: 320px;
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 8px;
  shadow-opacity: 0.25;
  shadow-radius: 16px;
  elevation: 8;
`;

export const ModalTitle = styled(Text)`
  font-size: 20px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
  margin-bottom: 12px;
`;

export const ModalMessage = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  text-align: center;
  line-height: 22px;
  margin-bottom: 24px;
`;

export const ButtonContainer = styled(View)`
  flex-direction: row;
  gap: 12px;
`;

export const CancelButton = styled(Pressable)<{ disabled?: boolean }>`
  flex: 1;
  padding: 14px 20px;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: transparent;
  align-items: center;
  justify-content: center;
  opacity: ${({ disabled }) => (disabled ? 0.6 : 1)};
`;

export const ConfirmButton = styled(Pressable)<{ disabled?: boolean }>`
  flex: 1;
  padding: 14px 20px;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.error};
  align-items: center;
  justify-content: center;
  shadow-color: ${({ theme }) => theme.colors.error};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.2;
  shadow-radius: 4px;
  elevation: 2;
  opacity: ${({ disabled }) => (disabled ? 0.6 : 1)};
`;

export const ButtonText = styled(Text)<ButtonTextProps>`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme, variant }) =>
    variant === "cancel" ? theme.colors.text : theme.colors.white};
`;
