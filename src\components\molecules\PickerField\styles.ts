import { styled } from "@/utils/styled";
import { View, Pressable, Text } from "react-native";

export const Container = styled(View)`
  margin-bottom: 16px;
  gap: 8px;
`;

export const PickerButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors.gray};
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.card};
`;

export const DisabledPickerButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors.gray};
  border-radius: 8px;
  background-color: ${({ theme }) =>
    theme.colors.disabled || theme.colors.gray + "20"};
  opacity: 0.6;
`;

export const PickerText = styled(Text)<{ disabled?: boolean }>`
  flex: 1;
  color: ${({ theme, disabled }) =>
    disabled ? theme.colors.gray : theme.colors.text};
  font-size: 16px;
`;

export const FlagContainer = styled(View)`
  margin-right: 8px;
  padding: 4px;
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.danger};
`;

export const RadioButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  border-width: 1px;
  width: 100%;
`;

export const SelectedIndicator = styled(View)`
  margin-left: auto;
`;

export const CheckboxContainer = styled(View)`
  padding: 4px;
  border-radius: 4px;
`;
