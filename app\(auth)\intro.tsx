import { Image } from "react-native";
import { useFocusEffect, useRouter } from "expo-router";
import {
  Container,
  Heading,
  Question,
  LinkText,
  ContentContainer,
  LinkContainer,
  LinkButton,
  TopContainer,
  BottomContentContainer,
  TopContentContainer,
  ButtonContainer,
} from "@/styles/Intro.styles";
import { useTheme } from "@/hooks/useTheme";
import { useTranslation } from "react-i18next";
import { useAppDispatch } from "@/store/store";
import { resetUser, setUser } from "@/store/slices/authSlice";
import { UserType } from "@/types/api";
import { useCallback } from "react";

export default function IntroScreen() {
  const router = useRouter();
  const { theme } = useTheme();
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  useFocusEffect(
    useCallback(() => {
      dispatch(resetUser());
    }, [])
  );
  const handleButtonPress = (role_id: number) => {
    dispatch(setUser({ role_id }));
    router.push("/(auth)/register");
  };

  return (
    <Container contentContainerStyle={{ flex: 1 }}>
      <TopContainer>
        <Image
          source={require("../../assets/icon.png")}
          style={{ height: "80%", width: "100%" }}
          resizeMode="contain"
        />
      </TopContainer>
      <ContentContainer>
        <TopContentContainer>
          <Heading>{t("intro.greeting")}</Heading>
          <Question>{t("intro.question")}</Question>
        </TopContentContainer>
        <BottomContentContainer>
          <ButtonContainer
            variant="outline"
            onPress={() => handleButtonPress(UserType.VENDOR)}
            textStyle={{ color: theme.colors.text }}
            title={t("intro.dealer_button")}
          />
          <ButtonContainer
            variant="outline"
            onPress={() => handleButtonPress(UserType.CUSTOMER)}
            textStyle={{ color: theme.colors.text }}
            title={t("intro.customer_button")}
          />
        </BottomContentContainer>
        <LinkContainer>
          <LinkButton onPress={() => router.push("/(auth)/login")}>
            <LinkText>{t("intro.have_account")}</LinkText>
          </LinkButton>
        </LinkContainer>
      </ContentContainer>
    </Container>
  );
}
