import React from "react";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
import { useTheme } from "@/hooks/useTheme";
import {
  HeaderContainer,
  Title,
  BackButton,
  ActionButton,
  CartBadge,
  CartBadgeText,
  TitleContainer,
  SideContainer,
} from "./styles";

interface HeaderProps {
  title: string;
  showCart?: boolean;
  showBack?: boolean;
  onBackPress?: () => void;
}

const Header = ({
  title,
  showCart = true,
  showBack = false,
  onBackPress,
}: HeaderProps) => {
  const router = useRouter();
  const { theme } = useTheme();
  const { cartList } = useSelector((state: RootState) => state.orders);

  return (
    <HeaderContainer>
      <SideContainer>
        {showBack && (
          <BackButton onPress={onBackPress || (() => router.back())}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </BackButton>
        )}
      </SideContainer>

      <TitleContainer>
        <Title>{title}</Title>
      </TitleContainer>

      <SideContainer>
        {showCart && (
          <ActionButton onPress={() => router.push("/(protected)/cart")}>
            <Ionicons name="cart-outline" size={24} color={theme.colors.text} />
            {cartList?.cartItems?.length > 0 && (
              <CartBadge>
                <CartBadgeText>{cartList?.cartItems?.length}</CartBadgeText>
              </CartBadge>
            )}
          </ActionButton>
        )}
      </SideContainer>
    </HeaderContainer>
  );
};

export default Header;
