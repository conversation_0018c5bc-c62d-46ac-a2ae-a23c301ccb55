import React, { useState } from "react";
import { Alert } from "react-native";
import { router } from "expo-router";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import {
  FormContainer,
  FormContent,
  FormInputGroup,
  FormLabel,
  FormInput,
  FormTextArea,
  ImageUploadButton,
  UploadPlaceholder,
  UploadText,
  UploadedImage,
  FormSubmitButton,
  FormSubmitButtonText,
} from "@/styles/Complaint.styles";
import { Header } from "@/components";
import { useTheme } from "@/hooks/useTheme";

export default function NewComplaintScreen() {
  const { t } = useTranslation();
  const [serialNumber, setSerialNumber] = useState("");
  const [description, setDescription] = useState("");
  const [image, setImage] = useState<string | null>(null);
  const { theme } = useTheme();
  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (status !== "granted") {
      Alert.alert(t("permission_needed"), t("photo_permission_message"));
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      setImage(result.assets[0].uri);
    }
  };

  const handleSubmit = () => {
    if (!serialNumber && !image) {
      Alert.alert(t("error.title"), t("validation.serial_or_image_required"));
      return;
    }

    if (!description) {
      Alert.alert(t("error.title"), t("validation.description_required"));
      return;
    }

    // TODO: Implement complaint submission logic
    console.log({
      serialNumber,
      description,
      image,
    });

    router.back();
  };

  return (
    <>
      <Header
        title={t("complaint.title")}
        showBack
        onBackPress={() => router.back()}
        showCart={false}
      />
      <FormContainer>
        <FormContent>
          {/* Serial Number Input */}
          <FormInputGroup>
            <FormLabel>{t("complaint.serial_number")}</FormLabel>
            <FormInput
              value={serialNumber}
              onChangeText={setSerialNumber}
              placeholder={t("complaint.enter_serial_number")}
              placeholderTextColor={theme.colors.gray}
            />
          </FormInputGroup>

          {/* Description Input */}
          <FormInputGroup>
            <FormLabel>{t("complaint.description")}</FormLabel>
            <FormTextArea
              value={description}
              onChangeText={setDescription}
              placeholder={t("complaint.enter_description")}
              placeholderTextColor={theme.colors.gray}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </FormInputGroup>

          {/* Image Upload */}
          <FormInputGroup>
            <FormLabel>{t("complaint.upload_image")}</FormLabel>
            <ImageUploadButton onPress={pickImage}>
              {image ? (
                <UploadedImage
                  source={{ uri: image }}
                  transition={200}
                  contentFit="cover"
                />
              ) : (
                <UploadPlaceholder>
                  <Ionicons
                    name="camera-outline"
                    size={32}
                    color={theme.colors.gray}
                  />
                  <UploadText>{t("complaint.tap_to_add_photo")}</UploadText>
                </UploadPlaceholder>
              )}
            </ImageUploadButton>
          </FormInputGroup>

          {/* Submit Button */}
          <FormSubmitButton onPress={handleSubmit}>
            <FormSubmitButtonText>{t("complaint.submit")}</FormSubmitButtonText>
          </FormSubmitButton>
        </FormContent>
      </FormContainer>
    </>
  );
}
