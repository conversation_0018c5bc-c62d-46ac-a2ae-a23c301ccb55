import React, { useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { View, Pressable } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { useAppDispatch, useAppSelector } from "@/store/store";
import {
  getCountryListAction,
  getStateListAction,
  getCityListAction,
} from "@/store/actions/auth";
import { CountryCode, StateList, CityList } from "@/types/auth";
import { useTheme } from "@/hooks/useTheme";
import FormField from "../../molecules/FormField";
import PickerField from "../../molecules/PickerField";
import Button from "../../atoms/Button";
import Text from "../../atoms/Text";
import CountryItem from "../../atoms/CountryItem";
import {
  Container,
  Form,
  SectionTitle,
  RadioGroup,
  RadioOption,
  RadioButton,
  RadioLabel,
} from "./styles";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { PickerOption } from "@/components/molecules/PickerField/PickerBottomSheet";

interface AddAddressFormProps {
  onSubmit: () => void;
  loading?: boolean;
  isEditMode?: boolean;
}

export interface AddAddressFormData {
  billing_shipping: number;
  contact_person_name: string;
  company_name: string;
  address_line_one: string;
  address_line_two: string;
  gst_number: string;
  post_code: string;
  country_id: number;
  state_id: number;
  city_id: number;
  phone: string;
}

const AddAddressForm: React.FC<AddAddressFormProps> = ({
  onSubmit,
  loading = false,
  isEditMode = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const { countryList, stateList, cityList } = useAppSelector(
    (state) => state.auth
  );
  const {
    formState: { isValid },
    setValue,
    watch,
  } = useFormContext();
  const [selectedCountry, setSelectedCountry] = useState<CountryCode | null>(
    null
  );
  const [selectedState, setSelectedState] = useState<StateList | null>(null);
  const [selectedCity, setSelectedCity] = useState<CityList | null>(null);
  const countryId = watch("country_id");
  const stateId = watch("state_id");
  const addressType = watch("billing_shipping");

  useEffect(() => {
    const fetchCountryList = async () => {
      if (!countryList?.length) {
        await dispatch(getCountryListAction({})).unwrap();
      }
    };
    fetchCountryList();
  }, [dispatch, countryList]);

  const handleCountrySelect = async (country: CountryCode) => {
    if (selectedCountry?.id === country.id) {
      setSelectedCountry(null);
      setValue("country_id", 0, { shouldValidate: true });
      setValue("state_id", 0);
      setValue("city_id", 0);
      setSelectedState(null);
      setSelectedCity(null);
      return false;
    }

    setSelectedCountry(country);
    setValue("country_id", country.id, { shouldValidate: true });
    setValue("state_id", 0);
    setValue("city_id", 0);
    setSelectedState(null);
    setSelectedCity(null);

    if (country.id && !stateList) {
      await dispatch(getStateListAction(country.id)).unwrap();
    }
    return true;
  };

  const handleStateSelect = async (state: StateList) => {
    if (selectedState?.id === state.id) {
      setSelectedState(null);
      setValue("state_id", 0, { shouldValidate: true });
      setValue("city_id", 0);
      setSelectedCity(null);
      return false;
    }

    setSelectedState(state);
    setValue("state_id", state.id, { shouldValidate: true });
    setValue("city_id", 0);
    setSelectedCity(null);

    if (state.id) {
      await dispatch(getCityListAction(state.id)).unwrap();
    }
    return true;
  };

  const handleCitySelect = (city: CityList) => {
    if (selectedCity?.id === city.id) {
      setSelectedCity(null);
      setValue("city_id", 0, { shouldValidate: true });
      return false;
    }

    setSelectedCity(city);
    setValue("city_id", city.id, { shouldValidate: true });
    return true;
  };

  const handleAddressTypeChange = (type: number) => {
    setValue("billing_shipping", type);
  };
  return (
    <Container>
      <KeyboardAwareScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <Form>
          <View style={{ marginBottom: 16 }}>
            <Text variant="subtitle" style={{ marginBottom: 8, color: theme.colors.text }}>
              {t("address_type")}{" "}
              <Text variant="subtitle" style={{ color: "red" }}>
                *
              </Text>
            </Text>
            <RadioGroup>
              <RadioOption>
                <Pressable
                  style={{ flexDirection: "row", alignItems: "center", gap: 8 }}
                  onPress={() => handleAddressTypeChange(1)}
                >
                  <RadioButton selected={addressType === 1}>
                    {addressType === 1 && (
                      <Ionicons
                        name="checkmark"
                        size={16}
                        color={theme.colors.white}
                      />
                    )}
                  </RadioButton>
                  <RadioLabel>Billing</RadioLabel>
                </Pressable>
              </RadioOption>

              <RadioOption>
                <Pressable
                  style={{ flexDirection: "row", alignItems: "center", gap: 8 }}
                  onPress={() => handleAddressTypeChange(2)}
                >
                  <RadioButton selected={addressType === 2}>
                    {addressType === 2 && (
                      <Ionicons
                        name="checkmark"
                        size={16}
                        color={theme.colors.white}
                      />
                    )}
                  </RadioButton>
                  <RadioLabel>Shipping</RadioLabel>
                </Pressable>
              </RadioOption>
            </RadioGroup>
          </View>

          <FormField
            name="contact_person_name"
            label={t("contact_person_name")}
            placeholder={t("enter_contact_person_name")}
            placeholderTextColor={theme.colors.gray}
          />

          <FormField
            name="company_name"
            label={t("company_name")}
            placeholder={t("enter_company_name")}
            placeholderTextColor={theme.colors.gray}
          />

          <FormField
            name="address_line_one"
            label={t("address_line_one")}
            rules={{ required: true }}
            placeholder={t("enter_address_line_one")}
            placeholderTextColor={theme.colors.gray}
          />

          <FormField
            name="address_line_two"
            label={t("address_line_two")}
            placeholder={t("enter_address_line_two")}
            placeholderTextColor={theme.colors.gray}
          />

          <FormField
            name="post_code"
            label={t("post_code")}
            placeholder={t("enter_post_code")}
            placeholderTextColor={theme.colors.gray}
            keyboardType="numeric"
            maxLength={6}
            rules={{ required: true }}
          />

          <PickerField
            name="country_id"
            label={t("country")}
            rules={{ required: true }}
            placeholder={t("select_country")}
            options={countryList || []}
            onSelect={handleCountrySelect}
            displayKey="full_name"
            renderItem={(item: PickerOption) => (
              <CountryItem
                item={item}
                isSelected={selectedCountry?.id === item.id}
                displayKey="full_name"
              />
            )}
          />

          <PickerField
            name="state_id"
            label={t("state")}
            rules={{ required: true }}
            placeholder={t("select_state")}
            options={stateList || []}
            onSelect={handleStateSelect}
            disabled={!countryId}
            displayKey="name"
            renderItem={(item: PickerOption) => (
              <CountryItem
                item={item}
                isSelected={selectedState?.id === item.id}
              />
            )}
          />

          <PickerField
            name="city_id"
            label={t("city")}
            rules={{ required: true }}
            placeholder={t("select_city")}
            options={cityList || []}
            onSelect={handleCitySelect}
            disabled={!stateId}
            displayKey="name"
            renderItem={(item: PickerOption) => (
              <CountryItem
                item={item}
                isSelected={selectedCity?.id === item.id}
              />
            )}
          />

          <FormField
            name="gst_number"
            label={t("gst_number")}
            placeholder={t("enter_gst_number")}
            placeholderTextColor={theme.colors.gray}
            autoCapitalize="characters"
          />
        </Form>
      </KeyboardAwareScrollView>
      <Button
        title={isEditMode ? t("update_address") : t("add_address")}
        onPress={onSubmit}
        loading={loading}
        disabled={!isValid || loading}
        style={{ marginVertical: 24, backgroundColor: theme.colors.primary }}
      />
    </Container>
  );
};

export default AddAddressForm;
