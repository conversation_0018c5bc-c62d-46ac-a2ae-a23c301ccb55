import { styled } from "@/utils/styled";
import { View, Text } from "react-native";

export const Container = styled(View)`
  background-color: ${({ theme }) => theme.colors.card};
  padding: 20px;
  border-radius: 16px;
  margin-bottom: 20px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.05;
  shadow-radius: 4px;
  elevation: 2;
`;

export const Header = styled(View)`
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
`;

export const Icon = styled(View)`
  background-color: ${({ theme }) => theme.colors.primary + "20"};
  border-radius: 8px;
  padding: 8px;
  margin-right: 12px;
`;

export const Title = styled(Text)`
  font-size: 18px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
`;

export const Content = styled(View)`
  gap: 12px;
`;

export const MethodContainer = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

export const MethodLabel = styled(Text)`
  font-size: 14px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
`;

export const MethodValue = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.primary};
  font-weight: 600;
`;

export const ProofContainer = styled(View)`
  flex-direction: row;
  align-items: center;
  gap: 8px;
`;

export const ProofStatus = styled(View)`
  flex-direction: row;
  align-items: center;
`;

export const ProofStatusText = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
  font-weight: 500;
`;
