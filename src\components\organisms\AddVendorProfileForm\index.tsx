import React, { useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import FormField from "../../molecules/FormField";
import SwitchField from "../../molecules/SwitchField";
import Button from "../../atoms/Button";
import Text from "../../atoms/Text";
import {
  Container,
  WelcomeText,
  Subtitle,
  Form,
  ButtonRow,
  BackButton,
  BackButtonText,
} from "./styles";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

interface AddVendorProfileFormProps {
  onSubmit: () => void;
  onBack: () => void;
  loading?: boolean;
}

export interface AddVendorProfileFormData {
  gst_number: string;
  auth_dealer: boolean;
}

const AddVendorProfileForm: React.FC<AddVendorProfileFormProps> = ({
  onSubmit,
  onBack,
  loading = false,
}) => {
  const { t } = useTranslation();
  const {
    formState: { isValid },
    trigger,
    watch,
  } = useFormContext();

  // Watch form values to detect if form has data
  const formValues = watch();

  // Trigger validation when component mounts with existing data
  useEffect(() => {
    const hasExistingData =
      formValues.gst_number || formValues.auth_dealer !== undefined;

    if (hasExistingData) {
      // Trigger validation for all fields to update isValid state
      trigger();
    }
  }, []); // Only run on mount

  return (
    <Container>
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
        <Form>
          <WelcomeText>
            {t("complete_profile")} <Text style={{ fontSize: 22 }}>📝</Text>
          </WelcomeText>
          <Subtitle>{t("fill_profile_details")}</Subtitle>

          <FormField
            name="gst_number"
            placeholder={t("gstin")}
            label={t("gstin")}
            autoCapitalize="characters"
            rules={{ required: true }}
            
          />

          <SwitchField
            name="auth_dealer"
            label={t("authorized_dealer")}
            size="medium"
          />
        </Form>
      </KeyboardAwareScrollView>
      <ButtonRow>
        <BackButton onPress={onBack}>
          <BackButtonText>{t("back")}</BackButtonText>
        </BackButton>

        <Button
          title={t("complete_profile")}
          onPress={onSubmit}
          loading={loading}
          disabled={!isValid || loading}
          style={{ flex: 1 }}
        />
      </ButtonRow>
    </Container>
  );
};

export default AddVendorProfileForm;
