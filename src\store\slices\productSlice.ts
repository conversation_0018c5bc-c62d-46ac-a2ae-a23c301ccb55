import { createSlice } from "@reduxjs/toolkit";
import { logoutAction, logoutLocalAction } from "../actions/auth";
import { ProductListPayloadType, ProductType } from "../../types/product";
import {
  getProductCategoryListAction,
  getProductListAction,
} from "../actions/product";

interface ProductState {
  products: ProductType[];
  cart: ProductType[];
  filters: ProductListPayloadType | null;
  isLoading: boolean;
  error: string | null;
  categoryList: { id: number; title: string }[];
  current_page: number;
  last_page: number;
  loadedPages: number[];
}

const initialState: ProductState = {
  products: [],
  cart: [],
  filters: null,
  isLoading: false,
  error: null,
  categoryList: [],
  current_page: 1,
  last_page: 1,
  loadedPages: [],
};

const productSlice = createSlice({
  name: "product",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(getProductListAction.fulfilled, (state, action) => {
      state.isLoading = false;
      if (action.payload?.meta && action.payload.meta.slug) {
        return;
      } else {
        state.filters = action.payload.meta;
        const responseData = action.payload?.data;

        if (responseData) {
          state.current_page = responseData.current_page;
          state.last_page = responseData.last_page;

          if (!state.loadedPages.includes(responseData.current_page)) {
            state.loadedPages.push(responseData.current_page);
          }

          if (responseData.current_page === 1) {
            // Initial load or refresh - replace the products array
            state.products = responseData.data;
            state.loadedPages = [1]; // Reset loaded pages for fresh start
          } else {
            // Load more - concatenate new data to existing products
            state.products = [...state.products, ...responseData.data];
          }
        }
      }
    });
    builder.addCase(getProductListAction.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(getProductListAction.rejected, (state) => {
      state.isLoading = false;
    });
    builder.addCase(getProductCategoryListAction.fulfilled, (state, action) => {
      state.categoryList = action.payload.data;
      state.isLoading = false;
    });
    builder.addCase(getProductCategoryListAction.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(getProductCategoryListAction.rejected, (state) => {
      state.isLoading = false;
    });
    // Logout
    builder.addCase(logoutAction.fulfilled, () => {
      return initialState;
    });
    // Logout Local
    builder.addCase(logoutLocalAction.fulfilled, () => {
      return initialState;
    });
  },
});

export const {} = productSlice.actions;

export default productSlice.reducer;
