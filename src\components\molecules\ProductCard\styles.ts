import { styled } from "@/utils/styled";
import { Pressable, View, Text } from "react-native";
import { Image } from "expo-image";

export const Container = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 16px;
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 3;
`;

export const ProductImage = styled(View)`
  width: 100%;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const StyledProductImage = styled(Image)`
  width: 100%;
  height: 100%;
`;

export const ProductInfo = styled(View)`
  flex: 1;
`;

export const ProductName = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
`;

export const ProductPrice = styled(Text)`
  font-size: 18px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.primary};
  margin-bottom: 8px;
`;

export const ProductDescription = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: 12px;
  line-height: 20px;
`;

export const AddToCartButton = styled(View)`
  margin-top: auto;
`;
