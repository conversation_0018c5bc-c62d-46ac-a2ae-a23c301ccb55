import React, { Fragment } from "react";
import { SafeAreaView } from "react-native";
import { useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { RootState, useAppSelector } from "@/store/store";
import { useTheme } from "@/hooks/useTheme";
import { Header } from "@/components";
import {
  Content,
  Section,
  SectionTitle,
  InfoItem,
  InfoContent,
  InfoLabel,
  InfoValue,
  EditButton,
  EditButtonText,
} from "@/styles/Profile.styles";

const ProfilePreviewScreen = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { user, cityList, stateList, countryList } = useAppSelector(
    (state: RootState) => state.auth
  );

  const goBack = () => {
    router.back();
  };

  return (
    <Fragment>
      <Header
        title={t("profile.title")}
        showBack
        onBackPress={goBack}
        showCart={false}
      />
      <Content style={{ backgroundColor: theme.colors.background }}>
        {/* Personal Information Section */}
        <Section style={{ backgroundColor: theme.colors.card }}>
          <SectionTitle style={{ color: theme.colors.text }}>
            {t("profile.personalInfo")}
          </SectionTitle>

          <InfoItem>
            <Ionicons
              name="person-outline"
              size={24}
              color={theme.colors.text}
            />
            <InfoContent>
              <InfoLabel style={{ color: theme.colors.text }}>
                {t("profile.name")}
              </InfoLabel>
              <InfoValue style={{ color: theme.colors.text }}>
                {user?.name || t("profile.notSet")}
              </InfoValue>
            </InfoContent>
          </InfoItem>

          <InfoItem>
            <Ionicons name="call-outline" size={24} color={theme.colors.text} />
            <InfoContent>
              <InfoLabel style={{ color: theme.colors.text }}>
                {t("profile.mobile")}
              </InfoLabel>
              <InfoValue style={{ color: theme.colors.text }}>
                {user?.mobile || t("profile.notSet")}
              </InfoValue>
            </InfoContent>
          </InfoItem>

          <InfoItem>
            <Ionicons name="mail-outline" size={24} color={theme.colors.text} />
            <InfoContent>
              <InfoLabel style={{ color: theme.colors.text }}>
                {t("profile.email")}
              </InfoLabel>
              <InfoValue style={{ color: theme.colors.text }}>
                {user?.email || t("profile.notSet")}
              </InfoValue>
            </InfoContent>
          </InfoItem>

          <InfoItem>
            <Ionicons name="card-outline" size={24} color={theme.colors.text} />
            <InfoContent>
              <InfoLabel style={{ color: theme.colors.text }}>
                {t("profile.gstNumber")}
              </InfoLabel>
              <InfoValue style={{ color: theme.colors.text }}>
                {user?.gst_number || t("profile.notSet")}
              </InfoValue>
            </InfoContent>
          </InfoItem>

          <InfoItem>
            <Ionicons
              name="location-outline"
              size={24}
              color={theme.colors.text}
            />
            <InfoContent>
              <InfoLabel style={{ color: theme.colors.text }}>
                {t("profile.address")}
              </InfoLabel>
              <InfoValue style={{ color: theme.colors.text }}>
                {[
                  user?.address_line_one,
                  user?.address_line_two,
                  cityList?.find((city) => city.id === user?.city_id)?.name,
                  stateList?.find((state) => state.id === user?.state_id)?.name,
                  user?.post_code,
                  countryList?.find(
                    (country) => country.id === user?.country_id
                  )?.full_name,
                ]
                  .filter(Boolean)
                  .join(", ") || t("profile.notSet")}
              </InfoValue>
            </InfoContent>
          </InfoItem>
        </Section>
      </Content>
      <EditButton
        style={{ backgroundColor: theme.colors.primary }}
        onPress={() => router.push("/profile/edit")}
      >
        <EditButtonText>{t("profile.editProfile")}</EditButtonText>
      </EditButton>
    </Fragment>
  );
};

export default ProfilePreviewScreen;
