import { useState } from 'react';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useAppDispatch } from '@/store/store';
import { logoutAction } from '@/store/actions/auth';
import Toast from 'react-native-toast-message';


export const useLogout = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = () => {
    setShowLogoutModal(true);
  };

  const cancelLogout = () => {
    setShowLogoutModal(false);
  };

  const confirmLogout = async () => {
    try {
      setIsLoggingOut(true);
      const response = await dispatch(logoutAction({})).unwrap();
      
      if (response.status) {
        Toast.show({
          type: "success",
          text1: response.message || t("logout.success"),
        });
        
        // Close modal and navigate to login
        setShowLogoutModal(false);
        router.replace("/");
      } else {
        Toast.show({
          type: "error",
          text1: response.message || t("logout.error"),
        });
      }
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: error?.message || t("logout.error"),
      });
    } finally {
      setIsLoggingOut(false);
    }
  };

  return {
    // State
    showLogoutModal,
    isLoggingOut,
    
    // Actions
    handleLogout,
    cancelLogout,
    confirmLogout,
    
    // Direct setters (for advanced use cases)
    setShowLogoutModal,
    setIsLoggingOut,
  };
};
