import React, { useState, useCallback } from "react";
import { ScrollView, View } from "react-native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { ProductImage, PriceText, Text } from "@/components/atoms";
import { PriceSummaryRow, PaymentInfoCard } from "@/components/molecules";
import { useTheme } from "@/hooks/useTheme";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { checkCouponCodeAction } from "@/store/actions/order";
import {
  Container,
  Header,
  Title,
  Subtitle,
  Section,
  SectionHeader,
  SectionIcon,
  SectionTitle,
  AddressContainer,
  AddressName,
  AddressText,
  ItemsContainer,
  ItemRow,
  ItemDetails,
  ItemName,
  ItemQuantity,
  PriceContainer,
  Divider,
  TotalContainer,
  TotalLabel,
  TotalAmount,
  CouponContainer,
  CouponInput,
  CouponButton,
  CouponButtonText,
  SavingsText,
} from "./styles";
import { UserType } from "@/types/api";
import { VendorDetails } from "@/types/vendor";
import { PaymentMethod } from "@/types/payment";
import * as ImagePicker from "expo-image-picker";
import { CartItem, Address } from "@/types/cart";
import { decodeDecimal } from "@/utils/common";

interface ProductImage {
  url?: string;
}

interface Product {
  id: number;
  title: string;
  main_image?: ProductImage;
}

interface CartSummaryData {
  sub_total: string | number;
  cgst_total: string | number;
  cgst_percentage: string | number;
  sgst_total: string | number;
  sgst_percentage: string | number;
  igst_total: string | number;
  igst_percentage: string | number;
  total_amount: string | number;
  cartItems: CartItem[];
}

interface OfferCartProduct {
  product: number;
  sub_total: string | number;
}

interface OfferCartList {
  product: OfferCartProduct[];
  sub_total: string | number;
  total_amount: string | number;
  discount_amount: string | number;
}

interface OrderSummaryProps {
  cartData: CartSummaryData;
  billingAddress: Address | null;
  shippingAddress: Address | null;
  shipToSameAddress: boolean;
  selectedVendor?: VendorDetails | null;
  selectedPaymentMethod?: PaymentMethod | null;
  paymentProof?: ImagePicker.ImagePickerAsset | null;
}

const OrderSummary: React.FC<OrderSummaryProps> = ({
  cartData,
  billingAddress,
  shippingAddress,
  shipToSameAddress,
  selectedVendor,
  selectedPaymentMethod,
  paymentProof,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  const [couponCode, setCouponCode] = useState<string>("");
  const [offerCartList, setOfferCartList] = useState<OfferCartList | null>(
    null
  );
  const [isCheckingCoupon, setIsCheckingCoupon] = useState<boolean>(false);
  const [couponError, setCouponError] = useState<string | null>(null);

  const isSalesPerson = user?.role_id === UserType.SALESPERSON;
  const displayShippingAddress = shipToSameAddress
    ? billingAddress
    : shippingAddress;

  const getPaymentMethodDisplayName = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.Account_Number:
        return "Bank Account";
      case PaymentMethod.UPI_Id:
        return "UPI ID";
      case PaymentMethod.QR_Code:
        return "QR Code";
      case PaymentMethod.Credit:
        return "Credit";
      default:
        return "Unknown";
    }
  };

  const handleCheckCoupon = useCallback(async () => {
    if (!couponCode.trim()) {
      setCouponError("Please enter a coupon code");
      return;
    }

    try {
      setIsCheckingCoupon(true);
      setCouponError(null);

      const response = await dispatch(
        checkCouponCodeAction({ coupon_code: couponCode.trim() })
      ).unwrap();

      setOfferCartList(response.data);
    } catch (error) {
      setCouponError(error.message);
      setOfferCartList(null);
      console.error("Coupon validation failed:", error);
    } finally {
      setIsCheckingCoupon(false);
    }
  }, [couponCode, dispatch]);

  const hasValidCoupon =
    offerCartList &&
    offerCartList.discount_amount &&
    Number(offerCartList?.discount_amount || 0) > 0;

  const getItemPrice = useCallback(
    (item: CartItem): string | number => {
      if (!hasValidCoupon) {
        return item.quantity * parseFloat(item.price);
      }

      const discountedItem = offerCartList?.product.find(
        (itm) => itm.product === item.product_id
      );

      return (
        discountedItem?.sub_total || item.quantity * parseFloat(item.price)
      );
    },
    [hasValidCoupon, offerCartList]
  );

  const renderAddress = useCallback(
    (address: Address) => (
      <AddressContainer>
        <AddressName>{address.contact_person_name}</AddressName>
        <AddressText>
          {address.address_line_one}
          {address.address_line_two && `, ${address.address_line_two}`}
          {`, ${address.post_code}`}
        </AddressText>
        {address.phone && <AddressText>Phone: {address.phone}</AddressText>}
      </AddressContainer>
    ),
    []
  );

  const renderCartItem = useCallback(
    (item: CartItem) => (
      <ItemRow key={item.id}>
        <ProductImage uri={item.product.main_image?.url} size="small" />
        <ItemDetails>
          <ItemName>{item.product.title}</ItemName>
          <ItemQuantity>
            Qty: {item.quantity} × ₹{item.price}
          </ItemQuantity>
        </ItemDetails>
        <View style={{ flexDirection: "column", gap: 8 }}>
          {hasValidCoupon && (
            <PriceText
              amount={item.quantity * parseFloat(item.price)}
              variant="primary"
              size="small"
              style={{
                textDecorationLine: "line-through",
                color: theme.colors.gray,
              }}
            />
          )}
          <PriceText amount={getItemPrice(item)} variant="bold" size="medium" />
        </View>
      </ItemRow>
    ),
    [hasValidCoupon, getItemPrice, theme.colors.gray]
  );
  const renderPriceBreakdown = useCallback(
    () => (
      <PriceContainer>
        <PriceSummaryRow
          hasDiscount={hasValidCoupon}
          label="Subtotal"
          amount={
            hasValidCoupon
              ? decodeDecimal(offerCartList?.sub_total) -
                decodeDecimal(offerCartList?.discount_amount)
              : cartData.sub_total || 0
          }
          variant="bold"
        />

        <PriceSummaryRow
          label="CGST"
          amount={cartData.cgst_total || 0}
          showPercentage
          percentage={cartData.cgst_percentage || 0}
          variant="bold"
        />

        <PriceSummaryRow
          label="SGST"
          amount={cartData.sgst_total || 0}
          showPercentage
          percentage={cartData.sgst_percentage || 0}
          variant="bold"
        />

        {parseFloat(String(cartData.igst_total || 0)) > 0 && (
          <PriceSummaryRow
            label="IGST"
            amount={cartData.igst_total || 0}
            showPercentage
            percentage={cartData.igst_percentage || 0}
            variant="bold"
          />
        )}

        <CouponContainer>
          <CouponInput
            placeholder="Enter coupon code"
            value={couponCode}
            onChangeText={setCouponCode}
            placeholderTextColor={theme.colors.text}
          />
          <CouponButton
            onPress={handleCheckCoupon}
            disabled={isCheckingCoupon || !couponCode.trim()}
          >
            <CouponButtonText>
              {isCheckingCoupon ? "Checking..." : "Apply"}
            </CouponButtonText>
          </CouponButton>
        </CouponContainer>

        {couponError && (
          <SavingsText style={{ color: theme.colors.error }}>
            {couponError}
          </SavingsText>
        )}

        {hasValidCoupon && (
          <SavingsText>
            You saved {offerCartList?.discount_amount || 0} !
          </SavingsText>
        )}

        <Divider />

        <TotalContainer>
          <TotalLabel>Total Amount</TotalLabel>
          <TotalAmount>
            {hasValidCoupon ? (
              <>
                <PriceText
                  variant="bold"
                  style={{
                    textDecorationLine: "line-through",
                    color: theme.colors.gray,
                    fontSize: 12,
                  }}
                  amount={cartData.total_amount || 0}
                />
                <PriceText
                  variant="bold"
                  style={{ color: theme.colors.text }}
                  amount={offerCartList?.total_amount || 0}
                />
              </>
            ) : (
              <PriceText
                amount={cartData.total_amount || 0}
                style={{ color: theme.colors.text }}
              />
            )}
          </TotalAmount>
        </TotalContainer>
      </PriceContainer>
    ),
    [
      cartData,
      couponCode,
      couponError,
      hasValidCoupon,
      isCheckingCoupon,
      offerCartList,
      theme.colors,
      handleCheckCoupon,
      setCouponCode,
    ]
  );

  return (
    <Container>
      <ScrollView showsVerticalScrollIndicator={false}>
        <Header>
          <Title>Order Summary</Title>
          <Subtitle>
            Review your order details before placing your order
          </Subtitle>
        </Header>

        <Section>
          <SectionHeader>
            <SectionIcon>
              <Ionicons
                name="receipt-outline"
                size={20}
                color={theme.colors.primary}
              />
            </SectionIcon>
            <SectionTitle>Billing Address</SectionTitle>
          </SectionHeader>
          {billingAddress && renderAddress(billingAddress)}
        </Section>

        <Section>
          <SectionHeader>
            <SectionIcon>
              <Ionicons
                name={
                  shipToSameAddress ? "checkmark-circle" : "location-outline"
                }
                size={20}
                color={theme.colors.success}
              />
            </SectionIcon>
            <SectionTitle>
              Shipping Address
              {shipToSameAddress && (
                <Text
                  style={{
                    fontSize: 14,
                    fontWeight: "400",
                    color: theme.colors.success,
                    marginLeft: 8,
                  }}
                >
                  (Same as billing)
                </Text>
              )}
            </SectionTitle>
          </SectionHeader>
          {displayShippingAddress && renderAddress(displayShippingAddress)}
        </Section>

        {isSalesPerson && selectedVendor && (
          <Section>
            <SectionHeader>
              <SectionIcon>
                <Ionicons
                  name="person-outline"
                  size={20}
                  color={theme.colors.primary}
                />
              </SectionIcon>
              <SectionTitle>{t("cart.selectedVendor")}</SectionTitle>
            </SectionHeader>
            <AddressContainer>
              <AddressName>
                {selectedVendor.first_name} {selectedVendor.last_name}
              </AddressName>
              {selectedVendor.email && (
                <AddressText>
                  {t("cart.vendorEmail")}: {selectedVendor.email}
                </AddressText>
              )}
            </AddressContainer>
          </Section>
        )}

        <Section>
          <SectionHeader>
            <SectionIcon>
              <Ionicons
                name="bag-outline"
                size={20}
                color={theme.colors.primary}
              />
            </SectionIcon>
            <SectionTitle>Items ({cartData.cartItems.length})</SectionTitle>
          </SectionHeader>
          <ItemsContainer>
            {cartData.cartItems.map(renderCartItem)}
          </ItemsContainer>
        </Section>

        <Section>
          <SectionHeader>
            <SectionIcon>
              <Ionicons
                name="calculator-outline"
                size={20}
                color={theme.colors.primary}
              />
            </SectionIcon>
            <SectionTitle>Price Details</SectionTitle>
          </SectionHeader>
          {renderPriceBreakdown()}
        </Section>

        {selectedPaymentMethod && (
          <PaymentInfoCard
            selectedPaymentMethod={selectedPaymentMethod}
            paymentProof={paymentProof}
          />
        )}
      </ScrollView>
    </Container>
  );
};

export default React.memo(OrderSummary);
