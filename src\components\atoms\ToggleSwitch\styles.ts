import { styled } from "@/utils/styled";
import { View, Text } from "react-native";

interface SizeProps {
  size?: "small" | "medium" | "large";
}

interface ThumbProps extends SizeProps {
  active?: boolean;
}

interface DisabledProps {
  disabled?: boolean;
}

export const Container = styled(View)`
  flex: 1;
  margin-vertical: 8px;
  gap: 10px;
`;

export const Label = styled(Text)`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text};
  flex: 1;
  margin-right: 12px;
`;

export const SwitchContainer = styled(View)<SizeProps & DisabledProps>`
  opacity: ${({ disabled }) => (disabled ? 0.5 : 1)};
`;

export const Track = styled(View)<SizeProps & DisabledProps>`
  width: ${({ size }) => {
    switch (size) {
      case "small":
        return "40px";
      case "large":
        return "60px";
      default:
        return "50px";
    }
  }};
  height: ${({ size }) => {
    switch (size) {
      case "small":
        return "22px";
      case "large":
        return "32px";
      default:
        return "26px";
    }
  }};
  border-radius: ${({ size }) => {
    switch (size) {
      case "small":
        return "11px";
      case "large":
        return "16px";
      default:
        return "13px";
    }
  }};
  justify-content: center;
  position: relative;
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 2;
`;

export const Thumb = styled(View)<ThumbProps>`
  width: ${({ size }) => {
    switch (size) {
      case "small":
        return "18px";
      case "large":
        return "28px";
      default:
        return "22px";
    }
  }};
  height: ${({ size }) => {
    switch (size) {
      case "small":
        return "18px";
      case "large":
        return "28px";
      default:
        return "22px";
    }
  }};
  border-radius: ${({ size }) => {
    switch (size) {
      case "small":
        return "9px";
      case "large":
        return "14px";
      default:
        return "11px";
    }
  }};
  background-color: ${({ theme }) => theme.colors.background};
  position: absolute;
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.2;
  shadow-radius: 3px;
  elevation: 3;
`;
