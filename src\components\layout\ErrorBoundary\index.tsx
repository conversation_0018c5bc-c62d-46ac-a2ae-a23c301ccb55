import React, { Component, ErrorInfo, ReactNode } from "react";
import { <PERSON><PERSON><PERSON>, ErrorText, Retry<PERSON>utton, RetryText } from "./styles";

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export default class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Uncaught error:", error, errorInfo);
  }

  public render() {
    if (this.state.hasError) {
      return (
        <Container>
          <ErrorText>Something went wrong</ErrorText>
          <ErrorText>{this.state.error?.message}</ErrorText>
          <RetryButton
            onPress={() => this.setState({ hasError: false, error: null })}
          >
            <RetryText>Try again</RetryText>
          </RetryButton>
        </Container>
      );
    }

    return this.props.children;
  }
}
