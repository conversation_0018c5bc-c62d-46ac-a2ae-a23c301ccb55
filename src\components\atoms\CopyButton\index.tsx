import React from "react";
import { ViewStyle } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { CopyButtonContainer, CopyButtonText } from "./styles";
import { useTheme } from "@/hooks/useTheme";

interface CopyButtonProps {
  onPress: () => void;
  text?: string;
  style?: ViewStyle;
}

const CopyButton: React.FC<CopyButtonProps> = ({
  onPress,
  text = "Copy",
  style,
}) => {
  const { theme } = useTheme();

  return (
    <CopyButtonContainer onPress={onPress} style={style}>
      <Ionicons name="copy-outline" size={20} color={theme.colors.primary} />
      <CopyButtonText>{text}</CopyButtonText>
    </CopyButtonContainer>
  );
};

export default CopyButton;
