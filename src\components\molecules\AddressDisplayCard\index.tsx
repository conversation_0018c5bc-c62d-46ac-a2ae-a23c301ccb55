import React from "react";
import { OrderAddress } from "@/types/order";
import {
  Con<PERSON>er,
  HeaderContainer,
  TypeBadge,
  TypeText,
  ContactName,
  CompanyName,
  AddressText,
  PostalCode,
  GSTNumber,
} from "./styles";

interface AddressDisplayCardProps {
  address: OrderAddress;
  type: "billing" | "shipping";
}


const AddressDisplayCard: React.FC<AddressDisplayCardProps> = ({
  address,
  type,
}) => {
  const formatAddress = () => {
    const parts = [
      address.address_line_one,
      address.address_line_two,
    ].filter(Boolean);
    return parts.join(", ");
  };

  return (
    <Container>
      <HeaderContainer>
        <ContactName>{address.contact_person_name}</ContactName>
        <TypeBadge type={type}>
          <TypeText type={type}>{type}</TypeText>
        </TypeBadge>
      </HeaderContainer>

      {address.company_name && (
        <CompanyName>{address.company_name}</CompanyName>
      )}

      <AddressText>{formatAddress()}</AddressText>

      <PostalCode>Postal Code: {address.post_code}</PostalCode>

      {address.gst_number && (
        <GSTNumber>GST: {address.gst_number}</GSTNumber>
      )}
    </Container>
  );
};

export default AddressDisplayCard;
