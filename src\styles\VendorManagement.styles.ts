import { FlatList } from "react-native";
import { VendorDetails as VendorDetailsType } from "@/types/vendor";
import { styled } from "@/utils/styled";

export const Container = styled.View`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const Content = styled.View`
  flex: 1;
  padding: 0 20px;
`;

export const SearchContainer = styled.View`
  padding: 12px 20px;
  padding-bottom: 8px;
`;

export const VendorListContainer = styled.View`
  flex: 1;
`;

export const VendorList = styled(
  FlatList as new () => FlatList<VendorDetailsType>
)`
  flex: 1;
`;

export const VendorCard = styled.Pressable`
  background-color: ${({ theme }) => theme.colors.cardBackground};
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 8px;
  elevation: 3;
`;

export const VendorHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
`;

export const VendorInfo = styled.View`
  flex: 1;
  margin-right: 16px;
`;

export const VendorName = styled.Text`
  font-size: 20px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 6px;
  line-height: 24px;
`;

export const VendorEmail = styled.Text`
  font-size: 15px;
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: 4px;
  line-height: 18px;
`;

export const VendorPhone = styled.Text`
  font-size: 15px;
  color: ${({ theme }) => theme.colors.textSecondary};
  line-height: 18px;
`;

export const VendorActions = styled.View`
  flex-direction: row;
  gap: 10px;
`;

export const ActionButton = styled.Pressable<{
  variant?: "edit" | "delete";
}>`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: ${({ theme, variant }) =>
    variant === "delete"
      ? theme.colors.errorBackground
      : theme.colors.primaryBackground};
  align-items: center;
  justify-content: center;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

export const VendorDetails = styled.View`
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.border};
  padding-top: 16px;
`;

export const DetailRow = styled.View`
  flex-direction: row;
  margin-bottom: 10px;
  align-items: flex-start;
`;

export const DetailLabel = styled.Text`
  font-size: 14px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textSecondary};
  width: 100px;
  margin-right: 12px;
  line-height: 18px;
`;

export const DetailValue = styled.Text`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
  flex: 1;
  line-height: 18px;
`;

export const EmptyContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
`;

export const EmptyIcon = styled.View`
  margin-bottom: 24px;
  opacity: 0.6;
`;

export const EmptyText = styled.Text`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
  line-height: 24px;
  margin-bottom: 8px;
`;

export const EmptySubtext = styled.Text`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
  line-height: 20px;
  margin-bottom: 24px;
`;

export const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
`;

export const InfoRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background-color: ${({ theme }) => theme.colors.cardBackground};
`;

export const ItemsCountText = styled.Text`
  font-size: 13px;
  color: ${({ theme }) => theme.colors.text};
  font-weight: 500;
`;

export const SortButtonRow = styled.Pressable`
  flex-direction: row;
  align-items: center;
`;

export const SortLabel = styled.Text`
  font-size: 13px;
  color: ${({ theme }) => theme.colors.text};
  margin-right: 4px;
`;

export const SortValueText = styled.Text`
  font-size: 13px;
  color: ${({ theme }) => theme.colors.primary};
  font-weight: 600;
  margin-right: 4px;
`;
export const ActiveFiltersContainer = styled.View`
  padding: 0 16px 8px;
`;

export const FilterChipsRow = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
`;

export const ActiveFilterChip = styled.Pressable`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.primary};
  padding: 6px 10px;
  border-radius: 16px;
  gap: 4px;
`;

export const ActiveFilterText = styled.Text`
  color: ${({ theme }) => theme.colors.white};
  font-size: 14px;
`;
