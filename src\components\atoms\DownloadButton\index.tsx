import React from "react";
import { ViewStyle } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { DownloadButtonContainer, DownloadButtonText } from "./styles";
import { useTheme } from "@/hooks/useTheme";

interface DownloadButtonProps {
  onPress: () => void;
  text?: string;
  style?: ViewStyle;
}

const DownloadButton: React.FC<DownloadButtonProps> = ({
  onPress,
  text = "Download",
  style,
}) => {
  const { theme } = useTheme();

  return (
    <DownloadButtonContainer onPress={onPress} style={style}>
      <Ionicons
        name="download-outline"
        size={20}
        color={theme.colors.primary}
      />
      <DownloadButtonText>{text}</DownloadButtonText>
    </DownloadButtonContainer>
  );
};

export default DownloadButton;
