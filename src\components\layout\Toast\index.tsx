import React from "react";
import {
  ErrorToast,
  BaseToastProps,
  BaseToast,
} from "react-native-toast-message";
import { useTheme } from "@/hooks/useTheme";
import { useTranslation } from "react-i18next";
import { StyledErrorToast, StyledSuccessToast } from "./styles";

const CustomErrorToast = (props: BaseToastProps) => {
  const { theme } = useTheme();
  const { t } = useTranslation();

  const text1 =
    props.text1 && typeof props.text1 === "string" && props.text1.trim()
      ? props.text1
      : t("common.error_message");
  console.log("error toast", text1, props.text2);

  return (
    <StyledErrorToast
      {...props}
      text1={text1}
      theme={theme}
      text1NumberOfLines={3}
    />
  );
};

const CustomSuccessToast = (props: BaseToastProps) => {
  const { theme } = useTheme();
  console.log("success toast", props.text1, props.text2);
  return <StyledSuccessToast {...props} theme={theme} text1NumberOfLines={3} />;
};

export const toastConfig = {
  success: (props: any) => <CustomSuccessToast {...props} />,
  error: (props: any) => <CustomErrorToast {...props} />,
};
