import { styled } from "@/utils/styled";
import { TextInput } from "react-native";

export const StyledInput = styled(TextInput)<{ error?: boolean }>`
  padding: 16px;
  border-radius: 8px;
  border: 1px solid ${({ theme, error }) => 
    error ? theme.colors.error : theme.colors.gray};
  background-color: ${({ theme }) => theme.colors.inputBackground || theme.colors.card};
  color: ${({ theme }) => theme.colors.text};
  font-size: 16px;
  margin-bottom: 8px;
`;
