import React from 'react';
import { ViewProps } from 'react-native';
import { StyledCard } from './styles';

interface CardProps extends ViewProps {
  children: React.ReactNode;
  onPress?: () => void;
  variant?: 'default' | 'elevated' | 'outlined';
}

const Card: React.FC<CardProps> = ({ 
  children, 
  onPress, 
  variant = 'default',
  ...props 
}) => {
  return (
    <StyledCard 
      variant={variant}
      onPress={onPress}
      {...props}
    >
      {children}
    </StyledCard>
  );
};

export default Card;
