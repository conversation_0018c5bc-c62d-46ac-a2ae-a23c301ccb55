import { createSlice } from "@reduxjs/toolkit";
import { ComplaintState } from "../../types/complaint";
import {
  getComplaintsAction,
  getComplaintDetailsAction,
  createComplaintAction,
} from "../actions/complaint";
import { logoutAction } from "../actions/auth";
import { logoutLocalAction } from "../actions/auth";

const initialState: ComplaintState = {
  complaints: [],
  complaintDetails: null,
  loading: false,
  error: null,
};

const complaintSlice = createSlice({
  name: "complaint",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Get all complaints
      .addCase(getComplaintsAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getComplaintsAction.fulfilled, (state, action) => {
        state.loading = false;
        state.complaints = action.payload.data;
      })
      .addCase(getComplaintsAction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to load complaints";
      })
      // Get complaint details
      .addCase(getComplaintDetailsAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getComplaintDetailsAction.fulfilled, (state, action) => {
        state.loading = false;
        state.complaintDetails = action.payload.data;
      })
      .addCase(getComplaintDetailsAction.rejected, (state, action) => {
        state.loading = false;
        state.error =
          action.error.message || "Failed to load complaint details";
      })
      // Create complaint
      .addCase(createComplaintAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createComplaintAction.fulfilled, (state, action) => {
        state.loading = false;
        state.complaints.push(action.payload);
      })
      .addCase(createComplaintAction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to create complaint";
      })
      // Logout
      .addCase(logoutAction.fulfilled, () => {
        return initialState;
      })
      // Logout Local
      .addCase(logoutLocalAction.fulfilled, () => {
        return initialState;
      });
  },
});

export default complaintSlice.reducer;
