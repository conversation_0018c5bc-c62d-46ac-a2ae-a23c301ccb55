import React, { useState } from 'react';
import { ImageProps } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/hooks/useTheme';
import { StyledImage, PlaceholderContainer } from './styles';

interface ProductImageProps extends Omit<ImageProps, 'source'> {
  uri?: string;
  size?: 'small' | 'medium' | 'large';
  borderRadius?: number;
}

/**
 * ProductImage - Atomic component for displaying product images
 * Handles loading states and fallback placeholders
 */
const ProductImage: React.FC<ProductImageProps> = ({
  uri,
  size = 'medium',
  borderRadius = 8,
  style,
  ...props
}) => {
  const { theme } = useTheme();
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const defaultUri = "https://picsum.photos/200?random=1";
  const imageUri = uri || defaultUri;

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
    setImageError(false);
  };

  if (imageError) {
    return (
      <PlaceholderContainer size={size} borderRadius={borderRadius} style={style}>
        <Ionicons 
          name="image-outline" 
          size={size === 'small' ? 20 : size === 'large' ? 40 : 30} 
          color={theme.colors.gray} 
        />
      </PlaceholderContainer>
    );
  }

  return (
    <StyledImage
      source={{ uri: imageUri }}
      size={size}
      borderRadius={borderRadius}
      onError={handleImageError}
      onLoad={handleImageLoad}
      style={style}
      {...props}
    />
  );
};

export default ProductImage;
