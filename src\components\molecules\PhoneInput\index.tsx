import React, { useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { MaterialIcons } from "@expo/vector-icons";
import { Image as ExpoImage } from "expo-image";
import CountryCodePickerBottomSheet from "../../organisms/CountryCodePickerBottomSheet";
import { CountryCode } from "@/types/auth";
import { useTheme } from "@/hooks/useTheme";
import Text from "../../atoms/Text";
import {
  Container,
  InputRow,
  CountryCodeButton,
  CountryCodeText,
  PhoneInputField,
} from "./styles";

interface PhoneInputProps {
  name: string;
  label?: string;
  placeholder?: string;
  countryCode: CountryCode | null;
  onCountrySelect: (country: CountryCode) => void;
  countryList?: CountryCode[];
  rules?: any;
}

const PhoneInput: React.FC<PhoneInputProps> = ({
  name,
  label,
  placeholder,
  countryCode,
  onCountrySelect,
  countryList = [],
  rules,
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const { theme } = useTheme();
  const [showCountrySheet, setShowCountrySheet] = useState(false);
  const error = errors[name];

  return (
    <Container>
      {label && (
        <Text>
          {label}
          {rules?.required && <Text style={{ color: "red" }}> *</Text>}
        </Text>
      )}
      <InputRow>
        <CountryCodeButton onPress={() => setShowCountrySheet(true)}>
          <ExpoImage
            source={{ uri: countryCode?.flag }}
            style={{ width: 20, height: 15, marginRight: 4 }}
            contentFit="contain"
          />
          <CountryCodeText>{`+${countryCode?.dialling_code} `}</CountryCodeText>
          <MaterialIcons
            name="keyboard-arrow-down"
            size={20}
            color={theme.colors.text}
          />
        </CountryCodeButton>
        <Controller
          control={control}
          name={name}
          render={({ field: { onChange, value } }) => (
            <PhoneInputField
              placeholder={placeholder}
              placeholderTextColor={theme.colors.gray}
              keyboardType="phone-pad"
              value={value ? value.toString() : ""}
              onChangeText={(text) => onChange(text ? parseInt(text, 10) : 0)}
              maxLength={10}
              error={!!error}
            />
          )}
        />
      </InputRow>
      {error && <Text variant="error">{error.message as string}</Text>}

      <CountryCodePickerBottomSheet
        isVisible={showCountrySheet}
        onClose={() => setShowCountrySheet(false)}
        onSelect={(country) => {
          onCountrySelect(country);
          setShowCountrySheet(false);
        }}
        data={countryList}
        title="Select Country Code"
        searchPlaceholder="Search country"
      />
    </Container>
  );
};

export default PhoneInput;
