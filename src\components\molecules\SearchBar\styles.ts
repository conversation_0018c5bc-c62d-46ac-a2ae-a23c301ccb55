import { styled } from "@/utils/styled";
import { TextInput, View } from "react-native";

export const SearchBarContainer = styled(View)`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.inputBackground};
  border-radius: 24px;
  padding-horizontal: 16px;
  height: 44px;
`;
export const SearchInput = styled(TextInput)`
  flex: 1;
  color: ${({ theme }) => theme.colors.text};
  font-size: 16px;
`;
