import { styled } from "@/utils/styled";
import { View, Text, Image } from "react-native";

export const PaymentDetailsContainer = styled(View)`
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

export const PaymentDetailRow = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

export const PaymentDetailLabel = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  font-weight: 500;
`;

export const PaymentDetailValue = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
  font-weight: 600;
`;

export const QRCodeContainer = styled(View)`
  align-items: center;
  padding: 16px;
`;

export const QRCodeImage = styled(Image)`
  width: 200px;
  height: 200px;
  margin-bottom: 16px;
  border-radius: 8px;
`;
