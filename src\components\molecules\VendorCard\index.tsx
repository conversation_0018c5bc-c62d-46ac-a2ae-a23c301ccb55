import React from "react";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import { useAppSelector } from "@/store/store";
import { VendorDetails } from "@/types/vendor";
import {
  Container,
  Header,
  Info,
  Name,
  Email,
  Actions,
  ActionButton,
  Details,
  DetailRow,
  DetailLabel,
  DetailValue,
  StatusBadge,
  StatusText,
} from "./styles";

interface VendorCardProps {
  vendor: VendorDetails;
  onEdit: () => void;
  onDelete: () => void;
  showDetails?: boolean;
}

const VendorCard: React.FC<VendorCardProps> = ({
  vendor,
  onEdit,
  onDelete,
  showDetails = true,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  // Get lists from Redux store
  const { countryList, stateList, cityList } = useAppSelector(
    (state) => state.auth
  );

  const getCityName = (cityId: number): string => {
    const city = cityList?.find((city) => city.id === cityId);
    return city?.name || "";
  };

  const getStateName = (stateId: number): string => {
    const state = stateList?.find((state) => state.id === stateId);
    return state?.name || "";
  };

  const getCountryName = (countryCode: string): string => {
    const country = countryList?.find(
      (country) => country.country_code_alpha === countryCode
    );
    return country?.full_name || "";
  };

  const formatAddress = () => {
    const parts = [
      vendor.address_line_one,
      vendor.address_line_two,
      getCityName(vendor.city_id),
      getStateName(vendor.state_id),
      vendor.post_code,
      getCountryName(vendor.country_code_alpha),
    ].filter(Boolean);
    return parts.length > 0 ? parts.join(", ") : null;
  };

  const getAuthDealerStatus = () => {
    if (
      typeof vendor.auth_dealer === "undefined" ||
      vendor.auth_dealer === null
    ) {
      return null;
    }
    return vendor.auth_dealer === "1" ? "authorized" : "unauthorized";
  };

  const authStatus = getAuthDealerStatus();

  return (
    <Container>
      <Header>
        <Info>
          <Name>
            {vendor.first_name} {vendor.last_name}
          </Name>
          {vendor.email ? (
            <Email>{vendor.email}</Email>
          ) : (
            <Email>{t("common.not_available")}</Email>
          )}
          {authStatus && (
            <StatusBadge status={authStatus}>
              <StatusText status={authStatus}>
                {authStatus === "authorized"
                  ? t("vendorManagement.authorized")
                  : t("vendorManagement.unauthorized")}
              </StatusText>
            </StatusBadge>
          )}
        </Info>
        <Actions>
          <ActionButton variant="edit" onPress={onEdit}>
            <Ionicons
              name="create-outline"
              size={20}
              color={theme.colors.primary}
            />
          </ActionButton>
          <ActionButton variant="delete" onPress={onDelete}>
            <Ionicons
              name="trash-outline"
              size={20}
              color={theme.colors.error}
            />
          </ActionButton>
        </Actions>
      </Header>

      {showDetails && (
        <Details>
          {vendor.gst_number && (
            <DetailRow>
              <DetailLabel>{t("vendorManagement.gstNumber")}:</DetailLabel>
              <DetailValue>{vendor.gst_number}</DetailValue>
            </DetailRow>
          )}
          {vendor.country_code_alpha && (
            <DetailRow>
              <DetailLabel>{t("vendorManagement.country")}:</DetailLabel>
              <DetailValue>
                {getCountryName(vendor.country_code_alpha)}
              </DetailValue>
            </DetailRow>
          )}
          {formatAddress() && (
            <DetailRow>
              <DetailLabel>{t("vendorManagement.address")}:</DetailLabel>
              <DetailValue>{formatAddress()}</DetailValue>
            </DetailRow>
          )}
        </Details>
      )}
    </Container>
  );
};

export default VendorCard;
