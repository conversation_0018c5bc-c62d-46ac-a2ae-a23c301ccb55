import React, { useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import CartItemList from "@/components/organisms/CartItemList";
import CartSummary from "@/components/organisms/CartSummary";
import CartFooter from "@/components/organisms/CartFooter";
import { ConfirmationModal } from "@/components/molecules";
import { useCartManagement } from "@/hooks/useCartManagement";
import type { CartSummaryData } from "@/types/cart";

interface CartStepProps {
  onContinue: () => void;
}

const CartStep: React.FC<CartStepProps> = ({ onContinue }) => {
  const { t } = useTranslation();
  const { cartList, handleQuantityChange, handleRemoveItem, confirmClearCart } =
    useCartManagement();

  const [showClearCartModal, setShowClearCartModal] = useState<boolean>(false);
  const [isClearingCart, setIsClearingCart] = useState<boolean>(false);

  const handleClearCart = useCallback(() => {
    setShowClearCartModal(true);
  }, []);

  const handleConfirmClearCart = useCallback(async () => {
    try {
      setIsClearingCart(true);
      await confirmClearCart();
      setShowClearCartModal(false);
    } catch (error) {
      console.log(error);
    } finally {
      setIsClearingCart(false);
    }
  }, [confirmClearCart]);

  return (
    <>
      <CartItemList
        items={cartList?.cartItems || []}
        onQuantityChange={handleQuantityChange}
        onRemoveItem={handleRemoveItem}
      />
      <CartSummary data={cartList} />
      <CartFooter
        onClearCart={handleClearCart}
        onContinue={onContinue}
        clearButtonTitle={t("cart.clear")}
        continueButtonTitle="Continue"
        clearButtonVariant="danger"
      />
      <ConfirmationModal
        visible={showClearCartModal}
        title={t("cart.clearTitle")}
        message={t("cart.clearMessage")}
        variant="warning"
        icon="trash-outline"
        confirmText={t("cart.clear")}
        cancelText={t("common.cancel")}
        onConfirm={handleConfirmClearCart}
        onCancel={() => setShowClearCartModal(false)}
        loading={isClearingCart}
      />
    </>
  );
};

export default CartStep;
