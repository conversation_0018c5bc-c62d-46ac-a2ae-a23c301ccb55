import React from "react";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import { FloatingActionButtonContainer } from "./styles";

interface FloatingActionButtonProps {
  onPress: () => void;
  icon?: keyof typeof Ionicons.glyphMap;
}

const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  onPress,
  icon = "add",
}) => {
  const { theme } = useTheme();

  return (
    <FloatingActionButtonContainer onPress={onPress}>
      <Ionicons name={icon} size={24} color={theme.colors.white} />
    </FloatingActionButtonContainer>
  );
};

export default FloatingActionButton;
