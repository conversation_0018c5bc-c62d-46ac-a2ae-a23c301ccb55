import React from "react";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import { OrderAddress } from "@/types/order";
import { SectionHeader } from "@/components/atoms";
import { AddressDisplayCard } from "@/components/molecules";
import { Card } from "@/components/molecules";

interface OrderAddressesProps {
  billingAddress: OrderAddress;
  shippingAddress: OrderAddress;
}

/**
 * OrderAddresses - Organism component for order addresses section
 * Displays billing and shipping addresses
 */
const OrderAddresses: React.FC<OrderAddressesProps> = ({
  billingAddress,
  shippingAddress,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  const isSameAddress = billingAddress.id === shippingAddress.id;

  return (
    <Card variant="elevated">
      <SectionHeader
        title={t("order.addresses")}
        icon={
          <Ionicons
            name="location-outline"
            size={20}
            color={theme.colors.primary}
          />
        }
      />

      <AddressDisplayCard address={billingAddress} type="billing" />

      {!isSameAddress && (
        <AddressDisplayCard address={shippingAddress} type="shipping" />
      )}

      {isSameAddress && (
        <SectionHeader
          isLast
          title={t("order.same_as_billing")}
          icon={
            <Ionicons
              name="checkmark-circle"
              size={16}
              color={theme.colors.success}
            />
          }
        />
      )}
    </Card>
  );
};

export default OrderAddresses;
