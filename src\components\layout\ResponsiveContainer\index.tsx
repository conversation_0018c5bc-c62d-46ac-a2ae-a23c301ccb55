import React from "react";
import { ViewProps } from "react-native";
import { Container, Content, Section, TwoColumnContainer, Column } from "./styles";

interface ResponsiveContainerProps extends ViewProps {
  children: React.ReactNode;
}

interface ResponsiveContentProps {
  children: React.ReactNode;
  showsVerticalScrollIndicator?: boolean;
}

interface ResponsiveSectionProps {
  children: React.ReactNode;
}

interface ResponsiveTwoColumnProps {
  leftColumn: React.ReactNode;
  rightColumn: React.ReactNode;
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  ...props
}) => {
  return <Container {...props}>{children}</Container>;
};

export const ResponsiveContent: React.FC<ResponsiveContentProps> = ({
  children,
  showsVerticalScrollIndicator = false,
}) => {
  return (
    <Content contentContainerStyle={{ paddingBottom: 24 }} showsVerticalScrollIndicator={showsVerticalScrollIndicator}>
      {children}
    </Content>
  );
};

export const ResponsiveSection: React.FC<ResponsiveSectionProps> = ({
  children,
}) => {
  return <Section>{children}</Section>;
};

export const ResponsiveTwoColumn: React.FC<ResponsiveTwoColumnProps> = ({
  leftColumn,
  rightColumn,
}) => {
  return (
    <TwoColumnContainer>
      <Column>{leftColumn}</Column>
      <Column>{rightColumn}</Column>
    </TwoColumnContainer>
  );
};

export default ResponsiveContainer;
