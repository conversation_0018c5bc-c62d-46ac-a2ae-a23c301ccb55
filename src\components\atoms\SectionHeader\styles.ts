import { styled } from "@/utils/styled";
import { View, Text } from "react-native";
import { css } from "styled-components";

export const Container = styled(View)<{ isLast?: boolean }>`
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.divider || "#E9ECEF"};
  ${({ isLast }) =>
    isLast &&
    css`
      border-bottom-width: 0px;
    `}
`;

export const IconContainer = styled(View)`
  margin-right: 12px;
  width: 24px;
  height: 24px;
  justify-content: center;
  align-items: center;
`;

export const Title = styled(Text)`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  flex: 1;
`;

export const Subtitle = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary || theme.colors.gray};
  margin-left: 8px;
`;
