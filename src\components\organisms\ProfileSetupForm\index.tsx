import React, { useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useAppDispatch, useAppSelector } from "@/store/store";
import {
  getCountryListAction,
  getStateListAction,
  getCityListAction,
  logoutAction,
} from "@/store/actions/auth";
import { CountryCode, StateList, CityList } from "@/types/auth";
import FormField from "../../molecules/FormField";
import PickerField from "../../molecules/PickerField";
import Button from "../../atoms/Button";
import { Container, Title, Form } from "./styles";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { PickerOption } from "@/components/molecules/PickerField/PickerBottomSheet";
import CountryItem from "@/components/atoms/CountryItem";
import Header from "../Header";
import { router } from "expo-router";
import { useTheme } from "@/hooks/useTheme";

interface ProfileSetupFormProps {
  onSubmit: () => void;
  loading?: boolean;
}

export interface ProfileSetupFormData {
  first_name: string;
  last_name: string;
  email: string;
  address_line_one: string;
  address_line_two: string;
  post_code: string;
  gst_number: string;
  country_id: number;
  state_id: number;
  city_id: number;
}

const ProfileSetupForm: React.FC<ProfileSetupFormProps> = ({
  onSubmit,
  loading = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const { countryList, stateList, cityList } = useAppSelector(
    (state) => state.auth
  );
  const {
    formState: { isValid },
    setValue,
    watch,
  } = useFormContext();
  const [selectedCountry, setSelectedCountry] = useState<CountryCode | null>(
    null
  );
  const [selectedState, setSelectedState] = useState<StateList | null>(null);
  const [selectedCity, setSelectedCity] = useState<CityList | null>(null);
  const countryId = watch("country_id");
  const stateId = watch("state_id");

  useEffect(() => {
    const fetchCountryList = async () => {
      if (!countryList) {
        await dispatch(getCountryListAction({})).unwrap();
      }
    };
    fetchCountryList();
  }, [dispatch, countryList]);

  const handleCountrySelect = async (country: CountryCode) => {
    if (selectedCountry?.id === country.id) {
      setSelectedCountry(null);
      setValue("country_id", 0, { shouldValidate: true });
      setValue("state_id", 0);
      setValue("city_id", 0);
      setSelectedState(null);
      setSelectedCity(null);
      return false;
    }

    setSelectedCountry(country);
    setValue("country_id", country.id, { shouldValidate: true });
    setValue("state_id", 0);
    setValue("city_id", 0);
    setSelectedState(null);
    setSelectedCity(null);

    if (country.id && !stateList) {
      await dispatch(getStateListAction(country.id)).unwrap();
    }
    return true; 
  };

  const handleStateSelect = async (state: StateList) => {
    if (selectedState?.id === state.id) {
      setSelectedState(null);
      setValue("state_id", 0, { shouldValidate: true });
      setValue("city_id", 0);
      setSelectedCity(null);
      return false;
    }

    setSelectedState(state);
    setValue("state_id", state.id, { shouldValidate: true });
    setValue("city_id", 0);
    setSelectedCity(null);

    if (state.id ) {
      try {
        await dispatch(getCityListAction(state.id)).unwrap();
      } catch (error) {
      }
    }
    return true;
  };

  const handleCitySelect = (city: CityList) => {
    if (selectedCity?.id === city.id) {
      setSelectedCity(null);
      setValue("city_id", 0, { shouldValidate: true });
      return false;
    }

    setSelectedCity(city);
    setValue("city_id", city.id, { shouldValidate: true });
    return true;
  };
  const handleBackPress = async () => {
    await dispatch(logoutAction({})).unwrap();
    router.replace("/(auth)/intro");
  };
  return (
    <Container>
      <Header
        title={t("complete_profile")}
        showCart={false}
        showBack
        onBackPress={handleBackPress}
      />
      <KeyboardAwareScrollView
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <Form>
          <FormField
            name="first_name"
            label={t("first_name")}
            placeholder={t("enter_first_name")}
            placeholderTextColor={theme.colors.gray}
            rules={{ required: true }}
          />

          <FormField
            name="last_name"
            label={t("last_name")}
            placeholder={t("enter_last_name")}
            placeholderTextColor={theme.colors.gray}
            rules={{ required: true }}
          />

          <FormField
            name="email"
            label={t("email")}
            placeholder={t("enter_email")}
            placeholderTextColor={theme.colors.gray}
            keyboardType="email-address"
            rules={{ required: true }}
          />

          <FormField
            name="address_line_one"
            label={t("address_line_one")}
            placeholder={t("enter_address")}
            placeholderTextColor={theme.colors.gray}
            rules={{ required: true }}
          />

          <FormField
            name="address_line_two"
            label={t("address_line_two")}
            placeholder={t("enter_address_two")}
            placeholderTextColor={theme.colors.gray}
          />

          <FormField
            name="post_code"
            label={t("post_code")}
            placeholder={t("enter_postal_code")}
            placeholderTextColor={theme.colors.gray}
            keyboardType="numeric"
            rules={{ required: true }}
          />

          <FormField
            name="gst_number"
            label={t("gstin")}
            placeholder={t("enter_gst_number")}
            placeholderTextColor={theme.colors.gray}
            maxLength={15}
            autoCapitalize="characters"
          />

          <PickerField
            name="country_id"
            label={t("country")}
            placeholder={t("select_country")}
            options={countryList || []}
            onSelect={handleCountrySelect}
            displayKey="full_name"
            rules={{ required: true }}
            renderItem={(item: PickerOption) => (
              <CountryItem
                item={item}
                isSelected={selectedCountry?.id === item.id}
                displayKey="full_name"
              />
            )}
          />

          <PickerField
            name="state_id"
            label={t("state")}
            placeholder={t("select_state")}
            options={stateList || []}
            onSelect={handleStateSelect}
            disabled={!countryId}
            displayKey="name"
            rules={{ required: true }}
            renderItem={(item: PickerOption) => (
              <CountryItem
                item={item}
                isSelected={selectedState?.id === item.id}
                displayKey="name"
              />
            )}
          />

          <PickerField
            name="city_id"
            label={t("city")}
            placeholder={t("select_city")}
            options={cityList || []}
            onSelect={handleCitySelect}
            disabled={!stateId}
            displayKey="name"
            rules={{ required: true }}
            renderItem={(item: PickerOption) => (
              <CountryItem
                item={item}
                isSelected={selectedCity?.id === item.id}
                displayKey="name"
              />
            )}
          />
        </Form>
      </KeyboardAwareScrollView>

      <Button
        title={t("save_profile")}
        onPress={onSubmit}
        loading={loading}
        disabled={!isValid || loading}
        style={{ marginTop: 16 }}
      />
    </Container>
  );
};

export default ProfileSetupForm;
