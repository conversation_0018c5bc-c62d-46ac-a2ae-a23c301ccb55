import React from "react";
import { useTranslation } from "react-i18next";
import {
  PaymentDetailsContainer,
  PaymentDetailRow,
  PaymentDetailLabel,
  PaymentDetailValue,
  QRCodeContainer,
  QRCodeImage,
} from "./styles";
import CopyButton from "@/components/atoms/CopyButton";
import DownloadButton from "@/components/atoms/DownloadButton";
import Text from "@/components/atoms/Text";
import { PaymentMethod } from "@/types/payment";

interface PaymentDetailsProps {
  method: PaymentMethod;
  onCopy?: (text: string) => void;
  onDownload?: () => void;
  paymentData?: any;
}

const PaymentDetails: React.FC<PaymentDetailsProps> = ({
  method,
  onCopy,
  onDownload,
  paymentData,
}) => {
  const { t } = useTranslation();

  const renderBankAccountDetails = () => {
    const accountNumber =
      paymentData?.accountNumber || paymentData?.identifier_value;
    const ifscCode =
      paymentData?.ifscCode || paymentData?.IFSC?.identifier_value;

    return (
      <>
        <PaymentDetailRow>
          <PaymentDetailLabel>
            {t("cart.payment.details.account_number")}
          </PaymentDetailLabel>
          <PaymentDetailValue>{accountNumber}</PaymentDetailValue>
        </PaymentDetailRow>
        <PaymentDetailRow>
          <PaymentDetailLabel>
            {t("cart.payment.details.ifsc_code")}
          </PaymentDetailLabel>
          <PaymentDetailValue>{ifscCode}</PaymentDetailValue>
        </PaymentDetailRow>
        {onCopy && (
          <CopyButton
            onPress={() =>
              onCopy(
                `${t(
                  "cart.payment.details.account_number"
                )} ${accountNumber}, ${t(
                  "cart.payment.details.ifsc_code"
                )} ${ifscCode}`
              )
            }
          />
        )}
      </>
    );
  };

  const renderUPIDetails = () => {
    const upiId = paymentData?.upiId || paymentData?.identifier_value;

    return (
      <>
        <PaymentDetailRow>
          <PaymentDetailLabel>
            {t("cart.payment.details.upi_id")}
          </PaymentDetailLabel>
          <PaymentDetailValue>{upiId}</PaymentDetailValue>
        </PaymentDetailRow>
        {onCopy && (
          <CopyButton
            onPress={() =>
              onCopy(`${t("cart.payment.details.upi_id")} ${upiId}`)
            }
          />
        )}
      </>
    );
  };

  const renderQRCodeDetails = () => {
    const qrImageUrl = paymentData?.qrImageUrl || paymentData?.qr_code?.url;

    return (
      <>
        <QRCodeContainer>
          <QRCodeImage
            source={{
              uri: qrImageUrl,
            }}
            resizeMode="contain"
          />
          {onDownload && <DownloadButton onPress={onDownload} />}
        </QRCodeContainer>
      </>
    );
  };

  const renderCreditDetails = () => (
    <Text variant="body" style={{ fontStyle: "italic" }}>
      {t("cart.payment.details.credit_selected")}
    </Text>
  );

  const renderMethodDetails = () => {
    switch (method) {
      case PaymentMethod.Account_Number:
        return renderBankAccountDetails();
      case PaymentMethod.UPI_Id:
        return renderUPIDetails();
      case PaymentMethod.QR_Code:
        return renderQRCodeDetails();
      case PaymentMethod.Credit:
        return renderCreditDetails();
      default:
        return null;
    }
  };
  if (method === PaymentMethod.Credit) {
    return null;
  }
  return (
    <PaymentDetailsContainer>{renderMethodDetails()}</PaymentDetailsContainer>
  );
};

export default PaymentDetails;
