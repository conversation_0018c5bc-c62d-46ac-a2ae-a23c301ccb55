import React, { useState } from "react";
import { Animated } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import {
  Container,
  ContentContainer,
  ContactName,
  AddressText,
  PhoneText,
  TypeBadge,
  TypeBadgeText,
  EditButton,
  EditButtonText,
  RadioButton,
  RadioButtonInner,
} from "./styles";

interface Address {
  id: number;
  contact_person_name: string;
  address_line_one: string;
  address_line_two?: string;
  post_code: string;
  phone?: string;
  billing_shipping: string;
}

interface AddressCardProps {
  address: Address;
  isSelected: boolean;
  onSelect: () => void;
  onEdit: () => void;
  type: "billing" | "shipping";
}

/**
 * AddressCard - Molecule component for address selection
 * Displays address details with selection and edit functionality
 */
const AddressCard: React.FC<AddressCardProps> = ({
  address,
  isSelected,
  onSelect,
  onEdit,
  type,
}) => {
  const { theme } = useTheme();
  const [scaleValue] = useState(new Animated.Value(1));

  const handlePress = () => {
    Animated.sequence([
      Animated.timing(scaleValue, {
        toValue: 0.98,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleValue, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
    onSelect();
  };

  return (
    <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
      <Container isSelected={isSelected} onPress={handlePress}>
        <ContentContainer>
          <ContactName>{address.contact_person_name}</ContactName>

          <TypeBadge type={type}>
            <TypeBadgeText type={type}>{type.toUpperCase()}</TypeBadgeText>
          </TypeBadge>

          <RadioButton isSelected={isSelected} onPress={handlePress}>
            {isSelected && <RadioButtonInner />}
          </RadioButton>
        </ContentContainer>

        <AddressText>
          {address.address_line_one}
          {address.address_line_two && `, ${address.address_line_two}`}
        </AddressText>

        <AddressText>{address.post_code}</AddressText>

        {address.phone && <PhoneText>{address.phone}</PhoneText>}

        <EditButton onPress={onEdit}>
          <Ionicons name="pencil" size={12} color={theme.colors.primary} />
          <EditButtonText>Edit</EditButtonText>
        </EditButton>
      </Container>
    </Animated.View>
  );
};

export default AddressCard;
