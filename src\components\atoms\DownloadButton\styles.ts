import { styled } from "@/utils/styled";
import { Pressable, Text } from "react-native";

export const DownloadButtonContainer = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  padding: 8px 12px;
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  margin-top: 8px;
`;

export const DownloadButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.primary};
  font-size: 14px;
  font-weight: 500;
  margin-left: 8px;
`;
