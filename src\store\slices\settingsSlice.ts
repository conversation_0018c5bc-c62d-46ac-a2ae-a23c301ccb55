import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { logoutAction, logoutLocalAction } from "../actions/auth";

export enum Language {
  EN = "en",
  HI = "hi",
  GU = "gu",
}

interface SettingsState {
  language: Language;
  isDarkMode: boolean;
  notifications: boolean;
}

const initialState: SettingsState = {
  language: Language.EN,
  isDarkMode: false,
  notifications: true,
};

const settingsSlice = createSlice({
  name: "settings",
  initialState,
  reducers: {
    setLanguage: (state, action: PayloadAction<Language>) => {
      state.language = action.payload;
    },
    toggleDarkMode: (state) => {
      state.isDarkMode = !state.isDarkMode;
    },
    toggleNotifications: (state) => {
      state.notifications = !state.notifications;
    },
  },
  extraReducers: (builder) => {
    // Logout
    builder.addCase(logoutAction.fulfilled, () => {
      return initialState;
    });
    // Logout Local
    builder.addCase(logoutLocalAction.fulfilled, () => {
      return initialState;
    });
  },
});

export const { setLanguage, toggleDarkMode, toggleNotifications } =
  settingsSlice.actions;
export default settingsSlice.reducer;
