import React from "react";
import { TextProps } from "react-native";
import { useTranslation } from "react-i18next";
import { StyledPriceText } from "./styles";
import { formatAmount } from "@/utils/common";

interface PriceTextProps extends TextProps {
  amount: string | number;
  currency?: string;
  variant?: "primary" | "secondary" | "bold";
  size?: "small" | "medium" | "large";
}

/**
 * PriceText - Atomic component for displaying formatted prices
 * Provides consistent price formatting across the app
 */
const PriceText: React.FC<PriceTextProps> = ({
  amount,
  currency,
  variant = "primary",
  size = "medium",
  ...props
}) => {
  const { t } = useTranslation();

    return (
    <StyledPriceText variant={variant} size={size} {...props}>
      {currency || t("currency.symbol")}
      {formatAmount(amount)}
    </StyledPriceText>
  );
};

export default PriceText;
