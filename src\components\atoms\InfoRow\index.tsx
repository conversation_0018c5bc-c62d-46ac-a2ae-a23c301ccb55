import React from "react";
import { Container, LabelContainer, ValueContainer, Label, Value } from "./styles";

interface InfoRowProps {
  label: string;
  value: string | number;
  variant?: "default" | "bold" | "accent";
}

const InfoRow: React.FC<InfoRowProps> = ({
  label,
  value,
  variant = "default",
}) => {
  return (
    <Container>
      <LabelContainer>
        <Label>{label}</Label>
      </LabelContainer>
      <ValueContainer>
        <Value variant={variant}>{value}</Value>
      </ValueContainer>
    </Container>
  );
};

export default InfoRow;
