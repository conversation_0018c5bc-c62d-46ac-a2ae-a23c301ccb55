import { styled } from "../utils/styled";
import { Dimensions, FlatList, Pressable, Text, View } from "react-native";

const { width } = Dimensions.get("window");

export const Container = styled.View`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
`;

export const Header = styled.View`
  padding: 16px;
`;

export const SearchContainer = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 8px;
  padding: 8px 12px;
  margin-bottom: 12px;
  position: relative;
  width: 100%;
  max-width: 600px;
  align-self: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  elevation: 1;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

export const SearchInput = styled.TextInput`
  flex: 1;
  margin-left: 8px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const ViewToggle = styled(Pressable)`
  position: absolute;
  right: 16px;
`;



export const FilterChip = styled.Pressable<{ active?: boolean }>`
  padding: 8px 16px;
  border-radius: 20px;
  margin-right: 8px;
  background-color: ${({ theme, active }) =>
    active ? theme.colors.primary : theme.colors.card};
`;

export const FilterChipText = styled.Text<{ active?: boolean }>`
  color: ${({ theme, active }) =>
    active ? theme.colors.white : theme.colors.text};
  font-size: 14px;
`;

export const ActiveFiltersContainer = styled.View`
  padding: 8px 16px;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const ActiveFilterChip = styled.Pressable`
  flex-direction: row;
  align-items: center;
  padding: 6px 12px;
  margin-right: 8px;
  margin-bottom: 8px;
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 16px;
`;

export const ActiveFilterText = styled.Text`
  color: ${({ theme }) => theme.colors.white};
  font-size: 12px;
  margin-right: 4px;
`;

export const FilterChipsRow = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
`;

export const ProductList = styled(FlatList)``;

export const ProductCard = styled.Pressable<{ isList?: boolean }>`
  flex: ${({ isList }) => (isList ? 1 : "none")};
  width: ${({ isList }) => (isList ? "100%" : width / 2 - 16)}px;
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 8px;
  margin: 8px;
  overflow: hidden;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
`;

export const ProductImage = styled.View`
  width: 100%;
  height: ${width / 2 - 16}px;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const ProductInfo = styled.View`
  padding: 12px;
`;

export const ProductName = styled.Text`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
`;

export const ProductPrice = styled.Text`
  font-size: 18px;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
  font-weight: bold;
`;

export const ProductDescription = styled.Text`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
  opacity: 0.8;
`;

export const SortContainer = styled.View`
  flex-direction: row;
  align-items: center;
  padding-horizontal: 16px;
  margin-bottom: 8px;
  justify-content: space-between;
`;

export const SortButton = styled.Pressable`
  flex-direction: row;
  align-items: center;
`;

export const SortText = styled.Text`
  color: ${({ theme }) => theme.colors.text};
  font-Size: 15px;
`;

export const SortValue = styled.Text`
  color: ${({ theme }) => theme.colors.primary};
  font-weight: 600;
  margin-right: 4px;
`;

export const ItemCount = styled.Text`
  color: ${({ theme }) => theme.colors.text};
  font-size: 15px;
`;

export const EmptyContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 32px;
`;

export const EmptyText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
  margin-top: 16px;
`;
