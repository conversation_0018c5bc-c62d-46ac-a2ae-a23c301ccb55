import React from "react";
import { Container, IconContainer, Title, Subtitle } from "./styles";

interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  isLast?: boolean;
}

const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  subtitle,
  icon,
  isLast,
}) => {
  return (
    <Container isLast={isLast}>
      {icon && <IconContainer>{icon}</IconContainer>}
      <Title>{title}</Title>
      {subtitle && <Subtitle>{subtitle}</Subtitle>}
    </Container>
  );
};

export default SectionHeader;
