import { styled } from "@/utils/styled";
import { Pressable, Text } from "react-native";

export const CopyButtonContainer = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  padding: 8px 12px;
  background-color: ${({ theme }) => theme.colors.lightGray};
  border: 1px solid ${({ theme }) => theme.colors.primary + "09"};
  border-radius: 8px;
  margin-top: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const CopyButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.primary};
  font-size: 14px;
  font-weight: 500;
  margin-left: 8px;
`;
