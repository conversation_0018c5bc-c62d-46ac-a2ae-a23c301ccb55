import React, { useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { VendorDetails } from "@/types/vendor";
import { CartFooter } from "@/components/organisms";
import VendorSelectionBottomSheet from "@/components/organisms/VendorSelectionBottomSheet";
import Toast from "react-native-toast-message";
import {
  Container,
  Title,
  VendorInputContainer,
  VendorInput,
  VendorInputText,
  PlaceholderText,
  DropdownIcon,
} from "./VendorStep.styles";
import { useTheme } from "@/hooks/useTheme";

interface VendorStepProps {
  onBack: () => void;
  onContinue: (selectedVendor: VendorDetails) => void;
  selectedVendor?: VendorDetails | null;
}

const VendorStep: React.FC<VendorStepProps> = ({
  onBack,
  onContinue,
  selectedVendor: initialSelectedVendor = null,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [selectedVendor, setSelectedVendor] = useState<VendorDetails | null>(
    initialSelectedVendor
  );
  const [showVendorBottomSheet, setShowVendorBottomSheet] = useState(false);

  const handleVendorInputPress = useCallback(() => {
    setShowVendorBottomSheet(true);
  }, []);

  const handleVendorSelect = useCallback((vendor: VendorDetails) => {
    setSelectedVendor(vendor);
    setShowVendorBottomSheet(false);
  }, []);

  const handleCloseBottomSheet = useCallback(() => {
    setShowVendorBottomSheet(false);
  }, []);

  const handleContinue = useCallback(() => {
    if (!selectedVendor) {
      Toast.show({
        type: "error",
        text1: t("validation.vendorRequired"),
      });
      return;
    }
    onContinue(selectedVendor);
  }, [selectedVendor, onContinue, t]);

  const getVendorDisplayName = useCallback((vendor: VendorDetails) => {
    return `${vendor.first_name} ${vendor.last_name}`;
  }, []);

  return (
    <>
      <Container>
        <Title>{t("cart.selectVendor")}</Title>

        <VendorInputContainer onPress={handleVendorInputPress}>
          <VendorInput>
            {selectedVendor ? (
              <VendorInputText>
                {getVendorDisplayName(selectedVendor)}
              </VendorInputText>
            ) : (
              <PlaceholderText>{t("cart.selectVendor")}</PlaceholderText>
            )}
          </VendorInput>
          <DropdownIcon name="chevron-down" size={20} />
        </VendorInputContainer>
      </Container>

      <CartFooter
        onClearCart={onBack}
        onContinue={handleContinue}
        clearButtonTitle={t("common.back")}
        continueButtonTitle={t("continue")}
        disabled={!selectedVendor}
      />

      <VendorSelectionBottomSheet
        isVisible={showVendorBottomSheet}
        onClose={handleCloseBottomSheet}
        onSelect={handleVendorSelect}
      />
    </>
  );
};

export default VendorStep;
