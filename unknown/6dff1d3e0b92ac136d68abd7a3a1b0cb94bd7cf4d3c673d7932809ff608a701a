import styled from "styled-components/native";
import { View, Text, Pressable } from "react-native";

export const ModalOverlay = styled(View)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.overlay};
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

export const ModalContent = styled(View)`
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 12px;
  width: 100%;
  max-width: 400px;
  min-height: 400px;
  max-height: 85%;
  elevation: 5;
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.25;
  shadow-radius: 3.84px;
  overflow: hidden;
`;

export const ModalHeader = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 10px 20px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.divider || "#E9ECEF"};
`;

export const ModalTitle = styled(Text)`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
`;

export const CloseButton = styled(Pressable)`
  padding: 4px;
`;
