import { styled } from "@/utils/styled";
import { View, Text, Pressable } from "react-native";

export const Container = styled(View)`
  background-color: ${({ theme }) => theme.colors.card || theme.colors.white};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
`;

export const TimelineContainer = styled(View)`
  margin-top: 8px;
`;

export const TimelineItem = styled(View)`
  flex-direction: row;
  margin-bottom: 16px;
`;

export const TimelineIndicator = styled(View)`
  align-items: center;
  margin-right: 16px;
  width: 24px;
`;

export const TimelineLine = styled(View)<{
  isLast: boolean;
  isCompleted: boolean;
  isCancelled?: boolean;
}>`
  width: 2px;
  height: 24px;
  background-color: ${({ theme, isCompleted, isCancelled }) => {
    if (isCancelled) return theme.colors.error || "#DC3545";
    return isCompleted
      ? theme.colors.success || "#28A745"
      : theme.colors.divider || "#E9ECEF";
  }};
  margin-top: 8px;
  ${({ isLast, isCancelled }) => isLast && !isCancelled && "display: none;"}
`;

export const TimelineContent = styled(View)`
  flex: 1;
  padding-top: 2px;
`;

export const TimelineTitle = styled(Text)<{
  isCompleted: boolean;
  isActive: boolean;
}>`
  font-size: 16px;
  font-weight: ${({ isActive, isCompleted }) =>
    isActive || isCompleted ? "600" : "400"};
  color: ${({ theme, isCompleted, isActive }) => {
    if (isCompleted || isActive) return theme.colors.text;
    return theme.colors.textSecondary || theme.colors.gray;
  }};
  margin-bottom: 4px;
`;

export const ApprovalButtonsContainer = styled(View)`
  flex-direction: row;
  gap: 12px;
  margin-top: 8px;
`;

export const ApprovalButton = styled(Pressable)<{
  variant: "approve" | "reject";
}>`
  flex: 1;
  padding: 8px 16px;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  background-color: ${({ theme, variant }) =>
    variant === "approve" ? theme.colors.success || "#28A745" : theme.colors.error || "#DC3545"};
`;

export const ApprovalButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 14px;
  font-weight: 600;
`;
