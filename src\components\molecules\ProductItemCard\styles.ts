import { styled } from "@/utils/styled";
import { View, Text } from "react-native";
import { Image } from "expo-image";

export const Container = styled(View)`
  flex-direction: row;
  align-items: center;
  padding: 12px;
  background-color: ${({ theme }) => theme.colors.background || "#F8F9FA"};
  border-radius: 8px;
  margin-bottom: 8px;
  border: 1px solid ${({ theme }) => theme.colors.divider || "#E9ECEF"};
`;

export const ImageContainer = styled(View)`
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.lightGray || "#F8F9FA"};
  margin-right: 12px;
  overflow: hidden;
`;

export const ProductImage = styled(Image)`
  width: 100%;
  height: 100%;
`;

export const InfoContainer = styled(View)`
  flex: 1;
  justify-content: center;
`;

export const ProductName = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
  line-height: 20px;
`;

export const ProductDetails = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary || theme.colors.gray};
  margin-bottom: 2px;
`;

export const PriceContainer = styled(View)`
  align-items: flex-end;
  justify-content: center;
`;

export const Price = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.primary};
`;

export const Subtotal = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary || theme.colors.gray};
  margin-top: 2px;
`;
