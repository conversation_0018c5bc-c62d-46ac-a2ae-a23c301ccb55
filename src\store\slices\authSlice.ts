import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  getCityListAction,
  getCountryListAction,
  getSettingsListAction,
  getStateListAction,
  getUserDetailsAction,
  loginOTPAction,
  loginOTPVerifyAction,
  logoutAction,
  logoutLocalAction,
  registerOTPAction,
  updateUserAction,
} from "../actions/auth";
import {
  CountryCode,
  CountryCodeListResponse,
  LoginOTPVerifyResponse,
  CityList,
  StateList,
  StateListResponse,
  UserDetails,
  CityListResponse,
  SettingsList,
} from "../../types/auth";

interface AuthState {
  user: UserDetails | null;
  token: string | null;
  countryList: CountryCode[] | null;
  stateList: StateList[] | null;
  cityList: CityList[] | null;
  isLoading: boolean;
  isCountryListLoading: boolean;
  isLoginLoading: boolean;
  isRegisterLoading: boolean;
  isVerifyLoading: boolean;
  isStateListLoading: boolean;
  isCityListLoading: boolean;
  isUpdateUserLoading: boolean;
  isUserDetailsLoading: boolean;
  settingsList: SettingsList | null;
}

const initialState: AuthState = {
  user: null,
  token: null,
  countryList: null,
  isLoading: false,
  isCountryListLoading: false,
  isLoginLoading: false,
  isRegisterLoading: false,
  isVerifyLoading: false,
  stateList: null,
  isStateListLoading: false,
  cityList: null,
  isCityListLoading: false,
  isUpdateUserLoading: false,
  isUserDetailsLoading: false,
  settingsList: null,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
    },
    resetUser: (state) => {
      state.user = null;
    },
    updateUser: (state, action: PayloadAction<Partial<AuthState["user"]>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Login OTP
      .addCase(loginOTPAction.pending, (state) => {
        state.isLoginLoading = true;
      })
      .addCase(loginOTPAction.fulfilled, (state) => {
        state.isLoginLoading = false;
      })
      .addCase(loginOTPAction.rejected, (state) => {
        state.isLoginLoading = false;
      })
      // User Details
      .addCase(getUserDetailsAction.pending, (state) => {
        state.isUserDetailsLoading = true;
      })
      .addCase(getUserDetailsAction.fulfilled, (state, action) => {
        state.isUserDetailsLoading = false;
        state.user = { ...state.user, ...action.payload.data };
      })
      .addCase(getUserDetailsAction.rejected, (state) => {
        state.isUserDetailsLoading = false;
      })
      // Register OTP
      .addCase(registerOTPAction.pending, (state) => {
        state.isRegisterLoading = true;
      })
      .addCase(registerOTPAction.fulfilled, (state, action) => {
        state.isRegisterLoading = false;
      })
      .addCase(registerOTPAction.rejected, (state) => {
        state.isRegisterLoading = false;
      })
      // Verify OTP
      .addCase(loginOTPVerifyAction.pending, (state) => {
        state.isVerifyLoading = true;
      })
      .addCase(
        loginOTPVerifyAction.fulfilled,
        (state, action: PayloadAction<LoginOTPVerifyResponse>) => {
          state.isVerifyLoading = false;
          if (!action.payload.is_sale_person) {
            state.user = action.payload.data.user_details;
            state.token = action.payload.data.token;
          }
        }
      )
      .addCase(loginOTPVerifyAction.rejected, (state) => {
        state.isVerifyLoading = false;
      })
      // Country List
      .addCase(
        getCountryListAction.fulfilled,
        (state, action: PayloadAction<CountryCodeListResponse>) => {
          state.countryList = action.payload.data;
          state.isCountryListLoading = false;
        }
      )
      .addCase(getCountryListAction.pending, (state) => {
        state.isCountryListLoading = true;
      })
      .addCase(getCountryListAction.rejected, (state) => {
        state.isCountryListLoading = false;
      })
      // State List
      .addCase(
        getStateListAction.fulfilled,
        (state, action: PayloadAction<StateListResponse>) => {
          state.stateList = action.payload.data;
          state.isStateListLoading = false;
        }
      )
      .addCase(getStateListAction.pending, (state) => {
        state.isStateListLoading = true;
      })
      .addCase(getStateListAction.rejected, (state) => {
        state.isStateListLoading = false;
      })
      // City List
      .addCase(
        getCityListAction.fulfilled,
        (state, action: PayloadAction<CityListResponse>) => {
          state.cityList = action.payload.data;
          state.isCityListLoading = false;
        }
      )
      .addCase(getCityListAction.pending, (state) => {
        state.isCityListLoading = true;
      })
      .addCase(getCityListAction.rejected, (state) => {
        state.isCityListLoading = false;
      })
      // Update User
      .addCase(updateUserAction.pending, (state) => {
        state.isUpdateUserLoading = true;
      })
      .addCase(updateUserAction.fulfilled, (state, action) => {
        state.isUpdateUserLoading = false;
      })
      .addCase(updateUserAction.rejected, (state) => {
        state.isUpdateUserLoading = false;
      })
      // Logout
      .addCase(logoutAction.fulfilled, () => {
        return initialState;
      })
      // Logout Local
      .addCase(logoutLocalAction.fulfilled, () => {
        return initialState;
      })
      // Settings List
      .addCase(getSettingsListAction.fulfilled, (state, action) => {
        state.settingsList = action.payload.data;
      });
  },
});

export const { setUser, updateUser, resetUser } = authSlice.actions;
export default authSlice.reducer;
