import { styled } from "@/utils/styled";
import { View } from "react-native";
import { Image } from "expo-image";
import { Ionicons } from "@expo/vector-icons";
import Text from "../Text";

export const Container = styled(View)`
  flex-direction: row;
  align-items: center;
  gap: 8px;
  width: 100%;
`;

export const Flag = styled(Image)`
  width: 20px;
  height: 20px;
`;

export const CountryName = styled(Text)`
  font-size: 16px;
  font-weight: 600;
`;

export const Checkbox = styled(Ionicons)``;

export const CheckboxContainer = styled(View)`
  margin-left: auto;
`;
