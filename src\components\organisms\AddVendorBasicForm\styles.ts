import { styled } from "@/utils/styled";
import { View, Text, Pressable, TextInput } from "react-native";

export const Container = styled(View)`
  flex: 1;
  
`;

export const Form = styled(View)``;


export const PhoneInput = styled(TextInput)`
  flex: 1;
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.card};
  color: ${({ theme }) => theme.colors.text};
  font-size: 16px;
`;
