import { styled } from "../utils/styled";

export const Container = styled.View`
  flex: 1;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const Title = styled.Text`
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const FilterSection = styled.View`
  margin-bottom: 16px;
`;

export const HeaderRow = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
`;

export const HeaderTitle = styled.Text`
  font-weight: bold;
  font-size: 18px;
  color: ${({ theme }) => theme.colors.text};
`;

export const Divider = styled.View`
  height: 1px;
  background-color: ${({ theme }) => theme.colors.gray};
  margin-bottom: 16px;
  margin-top: 2px;
`;

export const SectionTitle = styled.Text`
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 8px;
  color: ${({ theme }) => theme.colors.text};
`;

export const PriceRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 8px;
`;

export const PriceText = styled.Text`
  color: ${({ theme }) => theme.colors.text};
  font-weight: 600;
`;

export const FilterOption = styled.Pressable<{ selected: boolean }>`
  flex-direction: row;
  align-items: center;
  padding: 12px;
  background-color: ${({ selected, theme }) =>
    selected ? theme.colors.primary : theme.colors.gray};
  border-radius: 8px;
  margin-bottom: 8px;
`;

export const OptionText = styled.Text<{ selected: boolean }>`
  font-size: 14px;
  margin-left: 8px;
  color: ${({ selected, theme }) =>
    selected ? theme.colors.white : theme.colors.text};
`;

export const ButtonRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-top: 28px;
  gap: 12px;
`;

export const ResetButton = styled.Pressable`
  flex: 1;
  border-width: 1.5px;
  border-radius: 8px;
  padding-vertical: 12px;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.white};
  border-color: ${({ theme }) => theme.colors.primary};
`;

export const ResetText = styled.Text`
  font-weight: bold;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.primary};
`;

export const ApplyButton = styled.Pressable`
  flex: 1;
  border-radius: 8px;
  padding-vertical: 12px;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.primary};
`;

export const ApplyText = styled.Text`
  color: ${({ theme }) => theme.colors.white};
  font-weight: bold;
  font-size: 16px;
`;

