import { styled } from "@/utils/styled";
import { View, ScrollView } from "react-native";
import { Dimensions } from "react-native";

const { width } = Dimensions.get("window");

export const Container = styled(View)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const Content = styled(ScrollView)`
  flex: 1;
  padding: ${width > 768 ? "24px" : "16px"};
`;

export const Section = styled(View)`
  margin-bottom: 16px;
`;

export const TwoColumnContainer = styled(View)`
  flex-direction: ${width > 768 ? "row" : "column"};
  gap: 16px;
`;

export const Column = styled(View)`
  flex: ${width > 768 ? 1 : "none"};
`;
