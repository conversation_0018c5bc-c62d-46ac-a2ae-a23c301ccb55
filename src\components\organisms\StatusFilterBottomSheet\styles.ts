import { styled } from "@/utils/styled";

export const StyledBottomSheetView = styled.View`
  flex: 1;
`;

export const OptionsContainer = styled.View`
  padding: 16px;
  padding-bottom: 100px;
`;

export const OptionButtonsContainer = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
`;

export const Title = styled.Text`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
`;

export const OptionButton = styled.Pressable<{ isSelected: boolean }>`
  border-radius: 16px;
  border-width: ${({ isSelected }) => (isSelected ? 0 : 1)}px;
  border-color: ${({ theme, isSelected }) =>
    isSelected ? "transparent" : theme.colors.border};
  background-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.selectedItemBg : theme.colors.white};
  padding-vertical: 8px;
  padding-horizontal: 18px;
  align-items: center;
  justify-content: center;
`;

export const OptionText = styled.Text<{ isSelected: boolean }>`
  font-size: 15px;
  font-weight: ${({ isSelected }) => (isSelected ? "bold" : "500")};
  color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.text : theme.colors.gray};
`;

export const ButtonRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background};
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.border};
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  gap: 16px;
`;

export const ClearButton = styled.Pressable`
  padding: 12px 24px;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.white};
  text-align: center;
  justify-content: center;
  /* flex: 1; */
`;

export const ClearText = styled.Text`
  color: ${({ theme }) => theme.colors.primary};
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  justify-content: center;
`;

export const ApplyButton = styled.Pressable`
  padding: 12px 24px;
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 8px;
  flex: 1;
  text-align: center;
  justify-content: center;
`;

export const ApplyText = styled.Text`
  color: ${({ theme }) => theme.colors.white};
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  justify-content: center;
`;

export const HeaderRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
`;

export const CloseButton = styled.Pressable`
  padding: 4px;
`;

export const Divider = styled.View`
  height: 1px;
  background-color: ${({ theme }) => theme.colors.border};
  margin-bottom: 8px;
`;
