import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import * as Notifications from "expo-notifications";
import { logoutAction, logoutLocalAction } from "../actions/auth";

interface NotificationPreferences {
  newProducts: boolean;
  offers: boolean;
  updates: boolean;
  orderStatus: boolean;
}

interface NotificationsState {
  expoPushToken: string | null;
  preferences: NotificationPreferences;
  lastNotification: Notifications.Notification | null;
}

const initialState: NotificationsState = {
  expoPushToken: null,
  preferences: {
    newProducts: true,
    offers: true,
    updates: true,
    orderStatus: true,
  },
  lastNotification: null,
};

const notificationsSlice = createSlice({
  name: "notifications",
  initialState,
  reducers: {
    setExpoPushToken: (state, action: PayloadAction<string>) => {
      state.expoPushToken = action.payload;
    },
    updatePreferences: (
      state,
      action: PayloadAction<Partial<NotificationPreferences>>
    ) => {
      state.preferences = { ...state.preferences, ...action.payload };
    },
    setLastNotification: (
      state,
      action: PayloadAction<Notifications.Notification>
    ) => {
      state.lastNotification = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Logout
    builder.addCase(logoutAction.fulfilled, () => {
      return initialState;
    });
    // Logout Local
    builder.addCase(logoutLocalAction.fulfilled, () => {
      return initialState;
    });
  },
});

export const { setExpoPushToken, updatePreferences, setLastNotification } =
  notificationsSlice.actions;
export default notificationsSlice.reducer;
