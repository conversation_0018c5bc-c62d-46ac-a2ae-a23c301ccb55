import { styled } from "@/utils/styled";
import { View, Text } from "react-native";

export const Container = styled(View)`
  background-color: ${({ theme }) => theme.colors.background || "#F8F9FA"};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid ${({ theme }) => theme.colors.divider || "#E9ECEF"};
`;

export const HeaderContainer = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

export const TypeBadge = styled(View)<{ type: "billing" | "shipping" }>`
  background-color: ${({ theme, type }) =>
    type === "billing" ? theme.colors.primary + "20" : theme.colors.secondary + "20"};
  padding: 4px 8px;
  border-radius: 12px;
`;

export const TypeText = styled(Text)<{ type: "billing" | "shipping" }>`
  font-size: 12px;
  font-weight: 600;
  color: ${({ theme, type }) =>
    type === "billing" ? theme.colors.primary : theme.colors.secondary};
  text-transform: uppercase;
`;

export const ContactName = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
`;

export const CompanyName = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary || theme.colors.gray};
  margin-bottom: 8px;
`;

export const AddressText = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
  line-height: 20px;
  margin-bottom: 8px;
`;

export const PostalCode = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary || theme.colors.gray};
  margin-bottom: 4px;
`;

export const  GSTNumber = styled(Text)`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.textSecondary || theme.colors.gray};
`;
