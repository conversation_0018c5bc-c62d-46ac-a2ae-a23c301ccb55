import React from 'react';
import { Modal, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/hooks/useTheme';
import {
  ModalOverlay,
  ModalContent,
  IconContainer,
  ModalTitle,
  ModalMessage,
  ButtonContainer,
  CancelButton,
  ConfirmButton,
  ButtonText,
} from './styles';

export type ConfirmationVariant = 'default' | 'danger' | 'warning' | 'success' | 'info';

export interface ConfirmationModalProps {
  // Visibility
  visible: boolean;
  
  // Content
  title: string;
  message: string;
  icon?: keyof typeof Ionicons.glyphMap;
  variant?: ConfirmationVariant;
  
  // Button labels
  cancelText?: string;
  confirmText?: string;
  
  // Actions
  onCancel: () => void;
  onConfirm: () => void;
  
  // State
  loading?: boolean;
  disabled?: boolean;
  
  // Behavior
  cancelable?: boolean; 
  showIcon?: boolean;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  visible,
  title,
  message,
  icon,
  variant = 'default',
  cancelText = 'Cancel',
  confirmText = 'Confirm',
  onCancel,
  onConfirm,
  loading = false,
  disabled = false,
  cancelable = true,
  showIcon = true,
}) => {
  const { theme } = useTheme();

  const getIconName = (): keyof typeof Ionicons.glyphMap => {
    if (icon) return icon;
    
    switch (variant) {
      case 'danger':
        return 'warning-outline';
      case 'warning':
        return 'alert-circle-outline';
      case 'success':
        return 'checkmark-circle-outline';
      case 'info':
        return 'information-circle-outline';
      default:
        return 'help-circle-outline';
    }
  };

  const getIconColor = (): string => {
    switch (variant) {
      case 'danger':
        return theme.colors.error;
      case 'warning':
        return theme.colors.warning || '#FF9500';
      case 'success':
        return theme.colors.success;
      case 'info':
        return theme.colors.info || theme.colors.primary;
      default:
        return theme.colors.primary;
    }
  };

  const handleModalClose = () => {
    if (cancelable && !loading) {
      onCancel();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleModalClose}
    >
      <ModalOverlay>
        <ModalContent variant={variant}>
          {showIcon && (
            <IconContainer variant={variant}>
              <Ionicons
                name={getIconName()}
                size={32}
                color={getIconColor()}
              />
            </IconContainer>
          )}
          
          <ModalTitle variant={variant}>
            {title}
          </ModalTitle>
          
          <ModalMessage>
            {message}
          </ModalMessage>
          
          <ButtonContainer>
            <CancelButton
              onPress={onCancel}
              disabled={loading || disabled}
            >
              <ButtonText variant="cancel">
                {cancelText}
              </ButtonText>
            </CancelButton>
            
            <ConfirmButton
              variant={variant}
              onPress={onConfirm}
              disabled={loading || disabled}
            >
              {loading ? (
                <ActivityIndicator 
                  size="small" 
                  color={theme.colors.white} 
                />
              ) : (
                <ButtonText variant="confirm">
                  {confirmText}
                </ButtonText>
              )}
            </ConfirmButton>
          </ButtonContainer>
        </ModalContent>
      </ModalOverlay>
    </Modal>
  );
};

export default ConfirmationModal;
