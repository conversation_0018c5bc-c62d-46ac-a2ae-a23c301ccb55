import { createAsyncThunk } from "@reduxjs/toolkit";
import { withToastForError } from "../../utils/thunk";
import {
  ProductCategoryResponseType,
  ProductListPayloadType,
  ProductListResponse,
} from "../../types/product";
import api from "../../services/api";
import { ApiResponse } from "@/types/api";

export const getProductListAction = createAsyncThunk(
  "product/getProductList",
  withToastForError(
    async (
      body: ProductListPayloadType
    ): Promise<ProductListResponse & ApiResponse> => {
      const response = await api.get("/product-list", {
        params: body,
      });
      return { ...response.data, meta: body };
    }
  )
);
export const getProductCategoryListAction = createAsyncThunk(
  "product/getProductCategoryList",
  withToastForError(async (): Promise<ProductCategoryResponseType> => {
    const response = await api.get("/category-list");
    return response.data;
  })
);
