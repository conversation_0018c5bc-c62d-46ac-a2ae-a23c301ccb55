import { logoutLocalAction } from "../store/actions/auth";
import { store } from "../store/store";
import api from "./api";
import Toast from "react-native-toast-message";

export const apiInterceptorsRequest = api.interceptors.request.use(
  async (config) => {
    const token = store.getState().auth.token;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    // TODO: Remove this after testing
    // console.log("config", config);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export const apiInterceptorsResponse = api.interceptors.response.use(
  (response) => {
    // TODO: Remove this after testing
    // console.log("response", response);
    return response;
  },
  async (error) => {
    if (error.response && error.response.status === 401) {
      const res = await store.dispatch(logoutLocalAction({})).unwrap();
      Toast.show({
        text1: res.message,
        type: "error",
      });
    }
    return Promise.reject(error);
  }
);
