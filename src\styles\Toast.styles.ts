import { styled } from "../utils/styled";

export const ToastContainer = styled.View<{
  type: "success" | "error" | "info";
}>`
  position: absolute;
  bottom: 50px;
  left: 20px;
  right: 20px;
  padding: 16px;
  border-radius: 8px;
  flex-direction: row;
  align-items: center;
  background-color: ${(props) => {
    switch (props.type) {
      case "success":
        return ({ theme }) => theme.colors.success;
      case "error":
        return ({ theme }) => theme.colors.error;
      default:
        return ({ theme }) => theme.colors.info;
    }
  }};
  elevation: 5;
  shadow-color: ${({ theme }) => theme.colors.black};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.25;
  shadow-radius: 3.84px;
`;

export const ToastText = styled.Text`
  color: ${({ theme }) => theme.colors.white};
  font-size: 14px;
  flex: 1;
  margin-left: 12px;
`;

export const IconContainer = styled.View`
  width: 24px;
  height: 24px;
  justify-content: center;
  align-items: center;
`;
