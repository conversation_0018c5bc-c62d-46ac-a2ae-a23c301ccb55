import React from "react";
import { OrderStatusEnum } from "@/types/order";
import { Container, StatusText } from "./styles";

interface StatusIndicatorProps {
  status: OrderStatusEnum;
  label: string;
  size?: "small" | "medium" | "large";
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  label,
  size = "medium",
}) => {
  return (
    <Container
      status={status}
      size={size}
      accessibilityRole="text"
      accessibilityLabel={`Order status: ${label}`}
    >
      <StatusText status={status} size={size}>
        {label}
      </StatusText>
    </Container>
  );
};

export default StatusIndicator;
