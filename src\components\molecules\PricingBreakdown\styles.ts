import { styled } from "@/utils/styled";
import { View, Text } from "react-native";

export const Container = styled(View)`
  background-color: ${({ theme }) => theme.colors.card || theme.colors.white};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid ${({ theme }) => theme.colors.divider || "#E9ECEF"};
`;

export const Row = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

export const TotalRow = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  margin-top: 8px;
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.divider || "#E9ECEF"};
`;

export const Label = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary || theme.colors.gray};
`;

export const Value = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
  font-weight: 500;
`;

export const TotalLabel = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
`;

export const TotalValue = styled(Text)`
  font-size: 18px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.primary};
`;

export const TaxRow = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  padding-left: 16px;
`;

export const TaxLabel = styled(Text)`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.textSecondary || theme.colors.gray};
`;

export const TaxValue = styled(Text)`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.textSecondary || theme.colors.gray};
`;
