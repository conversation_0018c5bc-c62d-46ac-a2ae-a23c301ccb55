import React from "react";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import { PaymentMethod } from "@/types/payment";
import * as ImagePicker from "expo-image-picker";
import {
  Container,
  Header,
  Icon,
  Title,
  Content,
  MethodContainer,
  MethodLabel,
  MethodValue,
  ProofContainer,
  ProofStatus,
  ProofStatusText,
} from "./styles";

interface PaymentInfoCardProps {
  selectedPaymentMethod?: PaymentMethod | null;
  paymentProof?: ImagePicker.ImagePickerAsset | null;
}

const PaymentInfoCard: React.FC<PaymentInfoCardProps> = ({
  selectedPaymentMethod,
  paymentProof,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  const getPaymentMethodDisplayName = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.Account_Number:
        return t("cart.payment.methods.bank_account");
      case PaymentMethod.UPI_Id:
        return t("cart.payment.methods.upi_id");
      case PaymentMethod.QR_Code:
        return t("cart.payment.methods.qr_code");
      case PaymentMethod.Credit:
        return t("cart.payment.methods.credit");
      default:
        return t("cart.payment.methods.unknown");
    }
  };

  const isPaymentProofRequired =
    selectedPaymentMethod &&
    [
      PaymentMethod.Account_Number,
      PaymentMethod.UPI_Id,
      PaymentMethod.QR_Code,
    ].includes(selectedPaymentMethod);

  if (!selectedPaymentMethod) {
    return null;
  }

  return (
    <Container>
      <Header>
        <Icon>
          <Ionicons
            name="card-outline"
            size={20}
            color={theme.colors.primary}
          />
        </Icon>
        <Title>{t("cart.payment.title")}</Title>
      </Header>
      <Content>
        <MethodContainer>
          <MethodLabel>{t("cart.payment.method")}:</MethodLabel>
          <MethodValue>
            {getPaymentMethodDisplayName(selectedPaymentMethod)}
          </MethodValue>
        </MethodContainer>

        {isPaymentProofRequired && (
          <MethodContainer>
            <MethodLabel>{t("cart.payment.proof")}:</MethodLabel>
            <ProofContainer>
              <ProofStatus>
                <Ionicons
                  name={paymentProof ? "checkmark-circle" : "alert-circle"}
                  size={16}
                  color={
                    paymentProof ? theme.colors.success : theme.colors.warning
                  }
                />
              </ProofStatus>
              <ProofStatusText>
                {paymentProof
                  ? t("cart.payment.proof_uploaded")
                  : t("cart.payment.proof_required")}
              </ProofStatusText>
            </ProofContainer>
          </MethodContainer>
        )}
      </Content>
    </Container>
  );
};

export default PaymentInfoCard;
