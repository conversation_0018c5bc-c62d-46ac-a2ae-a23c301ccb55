import { styled } from "@/utils/styled";
import { View, Text, Pressable } from "react-native";

interface ContainerProps {
  isSelected: boolean;
}

interface TypeBadgeProps {
  type: "billing" | "shipping";
}

interface RadioButtonProps {
  isSelected: boolean;
}

export const Container = styled(Pressable)<ContainerProps>`
  background-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary + "10" : theme.colors.card};
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 12px;
  border-width: ${({ isSelected }) => (isSelected ? "2px" : "1px")};
  border-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.border};
`;

export const ContentContainer = styled(View)`
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 8px;
`;

export const ContactName = styled(Text)`
  font-size: 16px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  flex: 1;
`;

export const TypeBadge = styled(View)<TypeBadgeProps>`
  background-color: ${({ theme, type }) =>
    type === "billing"
      ? theme.colors.primary + "20"
      : theme.colors.success + "20"};
  padding: 4px 8px;
  border-radius: 12px;
  margin-left: 8px;
`;

export const TypeBadgeText = styled(Text)<TypeBadgeProps>`
  font-size: 10px;
  font-weight: 600;
  color: ${({ theme, type }) =>
    type === "billing" ? theme.colors.primary : theme.colors.success};
`;

export const AddressText = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
  line-height: 20px;
  margin-bottom: 6px;
`;

export const PhoneText = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
  font-weight: 500;
  margin-bottom: 12px;
`;

export const EditButton = styled(Pressable)`
  align-self: flex-start;
  margin-top: 12px;
  padding: 6px 12px;
  background-color: ${({ theme }) => theme.colors.gray + "20"};
  border-radius: 6px;
  flex-direction: row;
  align-items: center;
  gap: 4px;
`;

export const EditButtonText = styled(Text)`
  font-size: 12px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.primary};
  text-transform: uppercase;
`;

export const RadioButton = styled(Pressable)<RadioButtonProps>`
  width: 24px;
  height: 24px;
  border-radius: 12px;
  border-width: 2px;
  border-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.border};
  background-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : "transparent"};
  align-items: center;
  justify-content: center;
  margin-left: 12px;
`;

export const RadioButtonInner = styled(View)`
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.white};
`;
