import { createAsyncThunk } from "@reduxjs/toolkit";
import {
  OrderListPayloadType,
  OrderResponse,
  OrderStatusEnum,
  PlaceOrderPayloadType,
  UpdateCartQuantityPayloadType,
} from "../../types/order";
import { withToastForError } from "../../utils/thunk";
import { ApiResponse } from "../../types/api";
import api from "../../services/api";
import { AddToCartPayloadType } from "../../types/order";

export const getCartListAction = createAsyncThunk(
  "order/cartList",
  withToastForError(async (): Promise<OrderResponse> => {
    const response = await api.get("/cart-list");
    return response.data;
  })
);

export const addToCartAction = createAsyncThunk(
  "order/addToCart",
  withToastForError(
    async (body: AddToCartPayloadType): Promise<ApiResponse> => {
      const response = await api.post("/add-to-cart", body);
      return response.data;
    }
  )
);

export const removeFromCartAction = createAsyncThunk(
  "order/removeFromCart",
  withToastForError(async (id: number): Promise<ApiResponse> => {
    const response = await api.post(`/remove-from-cart/${id}`);
    return response.data;
  })
);
export const placeOrderAction = createAsyncThunk(
  "order/placeOrder",
  withToastForError(async (payload: FormData): Promise<ApiResponse> => {
    const response = await api.post("/place-order", payload, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  })
);

export const getOrdersListAction = createAsyncThunk(
  "order/getOrdersList",
  withToastForError(
    async (params: OrderListPayloadType): Promise<ApiResponse> => {
      const response = await api.get("/order-list", { params });
      return { ...response.data, meta: params };
    }
  )
);

export const getOrderStatusListAction = createAsyncThunk(
  "order/getOrderStatusList",
  withToastForError(async (): Promise<ApiResponse> => {
    const response = await api.get(`/order-status-list`);
    return response.data;
  })
);

export const updateCartQuantity = createAsyncThunk(
  "order/updateCartQuantity",
  withToastForError(
    async (payload: UpdateCartQuantityPayloadType): Promise<ApiResponse> => {
      const response = await api.post("/update-cart-quantity", payload);
      return response.data;
    }
  )
);

export const clearProductFromCartAction = createAsyncThunk(
  "order/clearProductFromCart",
  withToastForError(
    async (payload: { product_id: number }): Promise<ApiResponse> => {
      const response = await api.post("/clear-product", payload);
      return response.data;
    }
  )
);

export const clearCartAction = createAsyncThunk(
  "order/clearCart",
  withToastForError(async (): Promise<ApiResponse> => {
    const response = await api.post("/clear-cart");
    return response.data;
  })
);

export const getOrderDetailsAction = createAsyncThunk(
  "order/getOrderDetails",
  withToastForError(
    async (payload: { order_id: number }): Promise<ApiResponse> => {
      const response = await api.get(`/order-details`, { params: payload });
      return response.data;
    }
  )
);

export const checkCouponCodeAction = createAsyncThunk(
  "order/checkCouponCode",
  withToastForError(
    async (payload: { coupon_code: string }): Promise<ApiResponse> => {
      const response = await api.get(`/check-coupon-code`, { params: payload });
      return { ...response.data, ...payload };
    }
  )
);

export const orderStatusChangeAction = createAsyncThunk(
  "order/orderStatusChange",
  withToastForError(
    async (payload: {
      order_id: number;
      status: OrderStatusEnum;
      description?: string;
      vehical_no?: string;
      tracking?: string;
      tracking_url?: string;
      notes?: string;
      courier_name?: string;
    }): Promise<ApiResponse> => {
      const response = await api.post(`/order-status-change`, payload);
      return response.data;
    }
  )
);