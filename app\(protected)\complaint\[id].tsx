import React, { useEffect, useState } from "react";
import { router, useLocalSearchParams } from "expo-router";
import { useTranslation } from "react-i18next";
import { useAppDispatch, useAppSelector } from "@/store/store";
import {
  getComplaintDetailsAction,
  updateComplaintAction,
} from "@/store/actions/complaint";
import {
  Container,
  Content,
  Card,
  Title,
  Label,
  Value,
  StatusBadge,
  StatusText,
  ErrorText,
  EmptyContainer,
  EmptyText,
  ImageContainer,
  ComplaintImage,
  ActionButton,
  ActionButtonText,
  ActionButtonContainer,
  FullScreenModal,
  FullScreenImageContainer,
  FullScreenImage,
  CloseButton,
} from "@/styles/Complaint.styles";
import { LoadingOverlay, Header } from "@/components";
import { UserType } from "@/types/api";
import { ComplaintStatusEnum } from "@/types/complaint";
import Toast from "react-native-toast-message";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";

const ComplaintDetailScreen = () => {
  const { id } = useLocalSearchParams();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { complaintDetails } = useAppSelector((state) => state.complaints);
  const user = useAppSelector((state) => state.auth.user);
  const isSalesPerson = UserType.SALESPERSON === user.role_id;
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const { theme } = useTheme();

  useEffect(() => {
    const fetchComplaintDetails = async () => {
      try {
        setIsLoading(true);
        setError(null);
        await dispatch(getComplaintDetailsAction(id as string)).unwrap();
      } catch (err) {
        setError(t("error_loading_complaint"));
      } finally {
        setIsLoading(false);
      }
    };

    fetchComplaintDetails();
  }, [dispatch, id, t]);

  const handleStatusUpdate = async (newStatus: ComplaintStatusEnum) => {
    try {
      await dispatch(
        updateComplaintAction({
          id: id as string,
          status: newStatus,
        })
      ).unwrap();
    } catch (err) {
      Toast.show({
        type: "error",
        text1: t("error_updating_status"),
      });
    }
  };

  const getNextStatusOptions = (currentStatus: ComplaintStatusEnum, userRole: UserType) => {
    switch (currentStatus) {
      case ComplaintStatusEnum.InReview:
        if (userRole === UserType.SERVICEPERSON) {
          return [ComplaintStatusEnum.EstimationSent, ComplaintStatusEnum.Rejected];
        }
        return [];
      case ComplaintStatusEnum.EstimationSent:
        if (userRole === UserType.CUSTOMER || userRole === UserType.SALESPERSON || userRole === UserType.VENDOR) {
          return [ComplaintStatusEnum.Approved, ComplaintStatusEnum.Rejected];
        }
        return [];
      case ComplaintStatusEnum.Approved:
        if (userRole === UserType.SERVICEPERSON) {
          return [ComplaintStatusEnum.RepairInProgress];
        }
        return [];
      case ComplaintStatusEnum.RepairInProgress:
        if (userRole === UserType.SERVICEPERSON) {
          return [ComplaintStatusEnum.Resolved];
        }
        return [];
      default:
        return [];
    }
  };

  if (isLoading) {
    return <LoadingOverlay isLoading={isLoading} />;
  }

  if (error) {
    return (
      <EmptyContainer>
        <ErrorText>{error}</ErrorText>
      </EmptyContainer>
    );
  }

  if (!complaintDetails) {
    return (
      <EmptyContainer>
        <EmptyText>{t("complaint_not_found")}</EmptyText>
      </EmptyContainer>
    );
  }

  return (
    <>
      <Header
        title={t("complaint.title")}
        showBack
        onBackPress={() => router.back()}
        showCart={false}
      />
      <Container>
        <Content>
          <Card>
            <Title>{t("complaint.details")}</Title>
            <Label>{t("complaint.id")}</Label>
            <Value>{complaintDetails.id}</Value>

            <Label>{t("complaint.status")}</Label>
            <StatusBadge status={complaintDetails.status}>
              <StatusText status={complaintDetails.status}>
                {t(`status.${complaintDetails.status}`)}
              </StatusText>
            </StatusBadge>

            {complaintDetails.image && (
              <ImageContainer
                onPress={() => setSelectedImage(complaintDetails.image)}
              >
                <ComplaintImage
                  source={{ uri: complaintDetails.image }}
                  contentFit="contain"
                />
              </ImageContainer>
            )}

            <Label>{t("complaint.description")}</Label>
            <Value>{complaintDetails.description}</Value>

            <Label>{t("complaint.created_at")}</Label>
            <Value>
              {new Date(complaintDetails.created_at).toLocaleDateString()}
            </Value>

            {complaintDetails.warranty_status && (
              <>
                <Label>{t("complaint.warranty_status")}</Label>
                <Value>
                  {t(`warranty.${complaintDetails.warranty_status}`)}
                </Value>
              </>
            )}

            {complaintDetails.repair_cost && (
              <>
                <Label>{t("complaint.repair_cost")}</Label>
                <Value>₹{complaintDetails.repair_cost}</Value>
              </>
            )}

            {complaintDetails.resolution && (
              <>
                <Label>{t("complaint.resolution")}</Label>
                <Value>{complaintDetails.resolution}</Value>
              </>
            )}

            {user?.role_id &&
              getNextStatusOptions(complaintDetails.status, user.role_id).length > 0 && (
                <>
                  <Label>{t("complaint.update_status")}</Label>
                  <ActionButtonContainer>
                    {getNextStatusOptions(complaintDetails.status, user.role_id).map(
                      (status) => (
                        <ActionButton
                          key={status}
                          onPress={() => handleStatusUpdate(status)}
                        >
                          <ActionButtonText>
                            {t(`status.${status}`)}
                          </ActionButtonText>
                        </ActionButton>
                      )
                    )}
                  </ActionButtonContainer>
                </>
              )}
          </Card>
        </Content>
      </Container>

      <FullScreenModal
        visible={!!selectedImage}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setSelectedImage(null)}
      >
        <FullScreenImageContainer>
          {selectedImage && (
            <FullScreenImage
              source={{ uri: selectedImage }}
              contentFit="contain"
            />
          )}
          <CloseButton onPress={() => setSelectedImage(null)}>
            <Ionicons name="close" size={32} color={theme.colors.black} />
          </CloseButton>
        </FullScreenImageContainer>
      </FullScreenModal>
    </>
  );
};

export default ComplaintDetailScreen;
