import { styled } from "@/utils/styled";
import { Text } from "react-native";

export const StyledText = styled(Text)<{ 
  variant?: 'body' | 'title' | 'subtitle' | 'caption' | 'error';
  color?: string;
}>`
  color: ${({ theme, variant, color }) => {
    if (color) return color;
    switch (variant) {
      case 'title':
        return theme.colors.text;
      case 'subtitle':
        return theme.colors.textSecondary;
      case 'caption':
        return theme.colors.gray;
      case 'error':
        return theme.colors.error;
      default:
        return theme.colors.text;
    }
  }};
  
  font-size: ${({ variant }) => {
    switch (variant) {
      case 'title':
        return '24px';
      case 'subtitle':
        return '18px';
      case 'caption':
        return '12px';
      case 'error':
        return '14px';
      default:
        return '16px';
    }
  }};
  
  font-weight: ${({ variant }) => {
    switch (variant) {
      case 'title':
        return '700';
      case 'subtitle':
        return '600';
      default:
        return '400';
    }
  }};
`;
