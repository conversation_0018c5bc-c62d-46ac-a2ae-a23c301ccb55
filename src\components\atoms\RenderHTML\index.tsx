import React from "react";
import RNREnderHTML, { RenderHTMLProps } from "react-native-render-html";
import { useWindowDimensions } from "react-native";
import { useTheme } from "@/hooks/useTheme";
import { StyledRenderHTML } from "./styles";

interface RenderHTMLAtomProps extends Partial<RenderHTMLProps> {
  htmlContent: string;
}

const RenderHTML: React.FC<RenderHTMLAtomProps> = ({
  htmlContent,
  ...rest
}) => {
  const { width } = useWindowDimensions();
  const { theme } = useTheme();

  return (
    <StyledRenderHTML
      contentWidth={width}
      source={{ html: htmlContent }}
      theme={theme}
      {...rest}
    />
  );
};

export default RenderHTML;
