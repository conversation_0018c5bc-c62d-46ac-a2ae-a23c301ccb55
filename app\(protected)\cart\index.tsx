import React, { useState, useCallback, useEffect, useMemo } from "react";
import { View } from "react-native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { useFocusEffect, useRouter, useLocalSearchParams } from "expo-router";
import { useTheme } from "@/hooks/useTheme";
import { useCartManagement } from "@/hooks/useCartManagement";
import { useAddressManagement } from "@/hooks/useAddressManagement";
import { useAppSelector } from "@/store/store";
import Header from "@/components/organisms/Header";
import Stepper from "@/components/molecules/Stepper";
import {
  CartStep,
  VendorStep,
  AddressStep,
  PaymentStep,
  SummaryStep,
} from "@/components/cart";
import { CartButton } from "@/components/atoms";
import {
  Container,
  StepperContainer,
  ContentContainer,
  EmptyContainer,
  EmptyText,
} from "@/styles/Cart.styles";
import { CheckoutStep } from "@/types/cart";
import { UserType } from "@/types/api";
import { VendorDetails } from "@/types/vendor";
import { PaymentMethod } from "@/types/payment";
import * as ImagePicker from "expo-image-picker";
import { useCart } from "@/hooks/useCart";
import { Redirect } from "expo-router";

const CartScreen = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { theme } = useTheme();

  const { cartList, fetchCartList } = useCart();
  const { fetchUserDetails } = useAddressManagement();
  const user = useAppSelector((state) => state.auth.user);

  const isServicePerson = user?.role_id === UserType.SERVICEPERSON;
  if (isServicePerson) {
    return <Redirect href="/(protected)/(tabs)/home" />;
  }

  const [currentStep, setCurrentStep] = useState<CheckoutStep>(
    CheckoutStep.CART
  );

  const [selectedBillingAddressId, setSelectedBillingAddressId] = useState<
    number | null
  >(null);
  const [selectedShippingAddressId, setSelectedShippingAddressId] = useState<
    number | null
  >(null);
  const [useBillingAsShipping, setUseBillingAsShipping] =
    useState<boolean>(true);
  const [selectedVendor, setSelectedVendor] = useState<VendorDetails | null>(
    null
  );
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState<PaymentMethod | null>(null);
  const [paymentProof, setPaymentProof] =
    useState<ImagePicker.ImagePickerAsset | null>(null);

  const isSalesperson = user?.role_id === UserType.SALESPERSON;

  const stepData = useMemo(() => {
    const steps = [{ id: CheckoutStep.CART, label: t("cart.steps.cart") }];

    if (isSalesperson) {
      steps.push({ id: CheckoutStep.VENDOR_SELECTION, label: "Vendor" });
    }

    steps.push(
      { id: CheckoutStep.ADDRESS_PREVIEW, label: "Address" },
      { id: CheckoutStep.PAYMENT, label: "Payment" },
      { id: CheckoutStep.SUMMARY, label: "Summary" }
    );

    return steps;
  }, [isSalesperson]);

  // Helper function to get next step
  const getNextStep = useCallback(
    (currentStep: CheckoutStep): CheckoutStep => {
      if (currentStep === CheckoutStep.CART) {
        return isSalesperson
          ? CheckoutStep.VENDOR_SELECTION
          : CheckoutStep.ADDRESS_PREVIEW;
      }
      if (currentStep === CheckoutStep.VENDOR_SELECTION) {
        return CheckoutStep.ADDRESS_PREVIEW;
      }
      if (currentStep === CheckoutStep.ADDRESS_PREVIEW) {
        return CheckoutStep.PAYMENT;
      }
      if (currentStep === CheckoutStep.PAYMENT) {
        return CheckoutStep.SUMMARY;
      }
      return currentStep;
    },
    [isSalesperson]
  );

  // Helper function to get previous step
  const getPreviousStep = useCallback(
    (currentStep: CheckoutStep): CheckoutStep => {
      if (currentStep === CheckoutStep.SUMMARY) {
        return CheckoutStep.PAYMENT;
      }
      if (currentStep === CheckoutStep.PAYMENT) {
        return CheckoutStep.ADDRESS_PREVIEW;
      }
      if (currentStep === CheckoutStep.ADDRESS_PREVIEW) {
        return isSalesperson
          ? CheckoutStep.VENDOR_SELECTION
          : CheckoutStep.CART;
      }
      if (currentStep === CheckoutStep.VENDOR_SELECTION) {
        return CheckoutStep.CART;
      }
      return currentStep;
    },
    [isSalesperson]
  );

  useEffect(() => {
    if (user?.address && Array.isArray(user.address)) {
      const billingAddresses = user.address.filter(
        (addr: any) => addr.billing_shipping === "1"
      );
      if (billingAddresses.length > 0 && !selectedBillingAddressId) {
        setSelectedBillingAddressId(billingAddresses[0].id);
      }

      const shippingAddresses = user.address.filter(
        (addr: any) => addr.billing_shipping === "2"
      );
      if (shippingAddresses.length > 0 && !selectedShippingAddressId) {
        setSelectedShippingAddressId(shippingAddresses[0].id);
      }
    }
  }, [user, selectedBillingAddressId, selectedShippingAddressId]);

  useFocusEffect(
    useCallback(() => {
      fetchCartList();
      fetchUserDetails();
    }, [fetchCartList, fetchUserDetails])
  );

  const handleNextStep = useCallback(() => {
    const nextStep = getNextStep(currentStep);
    if (nextStep !== currentStep) {
      setCurrentStep(nextStep);
    }
  }, [currentStep, getNextStep]);

  const handlePreviousStep = useCallback(() => {
    const prevStep = getPreviousStep(currentStep);
    if (prevStep !== currentStep) {
      setCurrentStep(prevStep);
    }
  }, [currentStep, getPreviousStep]);

  const handleStepSelect = useCallback(
    (stepId: number) => {
      if (stepId <= currentStep) {
        setCurrentStep(stepId as CheckoutStep);
      }
    },
    [currentStep]
  );

  const handleVendorContinue = useCallback(
    (vendor: VendorDetails) => {
      setSelectedVendor(vendor);
      handleNextStep();
    },
    [handleNextStep]
  );

  const handlePaymentContinue = useCallback(
    (paymentMethod: PaymentMethod, proof?: ImagePicker.ImagePickerAsset) => {
      setSelectedPaymentMethod(paymentMethod);
      setPaymentProof(proof || null);
      handleNextStep();
    },
    [handleNextStep]
  );

  const renderStepContent = useCallback(() => {
    switch (currentStep) {
      case CheckoutStep.CART:
        return <CartStep onContinue={handleNextStep} />;

      case CheckoutStep.VENDOR_SELECTION:
        return (
          <VendorStep
            onBack={handlePreviousStep}
            onContinue={handleVendorContinue}
            selectedVendor={selectedVendor}
          />
        );

      case CheckoutStep.ADDRESS_PREVIEW:
        return (
          <AddressStep
            onBack={handlePreviousStep}
            onContinue={handleNextStep}
            selectedBillingAddressId={selectedBillingAddressId}
            selectedShippingAddressId={selectedShippingAddressId}
            useBillingAsShipping={useBillingAsShipping}
            setSelectedBillingAddressId={setSelectedBillingAddressId}
            setSelectedShippingAddressId={setSelectedShippingAddressId}
            setUseBillingAsShipping={setUseBillingAsShipping}
            selectedVendor={selectedVendor}
          />
        );

      case CheckoutStep.PAYMENT:
        return (
          <PaymentStep
            onBack={handlePreviousStep}
            onContinue={handlePaymentContinue}
          />
        );

      case CheckoutStep.SUMMARY:
        return (
          <SummaryStep
            onBack={handlePreviousStep}
            selectedBillingAddressId={selectedBillingAddressId}
            selectedShippingAddressId={selectedShippingAddressId}
            useBillingAsShipping={useBillingAsShipping}
            selectedVendor={selectedVendor}
            selectedPaymentMethod={selectedPaymentMethod}
            paymentProof={paymentProof}
          />
        );

      default:
        return null;
    }
  }, [
    currentStep,
    handleNextStep,
    handlePreviousStep,
    handleVendorContinue,
    handlePaymentContinue,
    selectedVendor,
    selectedBillingAddressId,
    selectedShippingAddressId,
    useBillingAsShipping,
    selectedPaymentMethod,
    setSelectedBillingAddressId,
    setSelectedShippingAddressId,
    setUseBillingAsShipping,
    paymentProof,
  ]);

  if (!cartList?.cartItems?.length) {
    return (
      <Container>
        <Header
          title={t("cart.title")}
          showCart={false}
          showBack
          onBackPress={() => router.back()}
        />
        <EmptyContainer style={{ backgroundColor: theme.colors.background }}>
          <Ionicons name="cart-outline" size={64} color={theme.colors.text} />
          <EmptyText style={{ color: theme.colors.text }}>
            {t("cart.empty")}
          </EmptyText>
          <CartButton
            title={t("cart.start_shopping")}
            onPress={() => router.push("/(protected)/(tabs)/products")}
            variant="primary"
            icon="storefront"
          />
        </EmptyContainer>
      </Container>
    );
  }

  return (
    <Container>
      <Header
        title={t("cart.title")}
        showCart={false}
        showBack
        onBackPress={() => router.back()}
      />
      <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
        <StepperContainer>
          <Stepper
            stepData={stepData}
            currentId={currentStep}
            setSelectedTabNav={handleStepSelect}
          />
        </StepperContainer>
        <ContentContainer>{renderStepContent()}</ContentContainer>
      </View>
    </Container>
  );
};

export default CartScreen;
