import { styled } from "@/utils/styled";
import { View, Text } from "react-native";

export const Container = styled(View)`
  flex: 1;
  padding: 16px;
`;

export const Form = styled(View)`
  gap: 16px;
`;

export const SectionTitle = styled(Text)`
  font-size: 20px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 16px;
  text-align: center;
`;

export const RadioGroup = styled(View)`
  flex-direction: row;
  gap: 20px;
`;

export const RadioOption = styled(View)`
  flex: 1;
`;

export const RadioButton = styled(View)<{ selected?: boolean }>`
  width: 24px;
  height: 24px;
  border-radius: 12px;
  border: 2px solid ${({ theme, selected }) => 
    selected ? theme.colors.primary : theme.colors.border};
  background-color: ${({ theme, selected }) => 
    selected ? theme.colors.primary : 'transparent'};
  align-items: center;
  justify-content: center;
`;

export const RadioLabel = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;
