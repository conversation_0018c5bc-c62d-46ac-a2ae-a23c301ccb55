import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { BottomSheetFlatList, BottomSheetModal } from "@gorhom/bottom-sheet";
import { Ionicons } from "@expo/vector-icons";

import { useTheme } from "@/hooks/useTheme";
import {
  HeaderRow,
  HeaderTitle,
  CloseButton,
  Divider,
  ButtonRow,
  ApplyButton,
  ApplyText,
  StyledBottomSheetModal,
  StyledBottomSheetView,
  FilterChip,
  FilterChipText,
  FilterContainer,
  ClearButton,
  ClearText,
} from "./styles";
import { useBottomSheetBackdrop } from "@/utils/commonBackdrop";

export interface FilterOptionList {
  id: number | null;
  title: string;
}

interface FilterBottomSheetProps {
  isVisible: boolean;
  onDismiss: () => void;
  onClose: () => void;
  onApply: (selectedCategories: number) => void;
  onClear: () => void;
  categoryList: { id: number; title: string }[];
  title?: string;
  clearText?: string;
  applyText?: string;
}

const FilterBottomSheet: React.FC<FilterBottomSheetProps> = ({
  isVisible,
  onDismiss,
  onApply,
  onClear,
  categoryList,
  title = "Filter",
  clearText = "Clear",
  applyText = "Apply",
}) => {
  const { theme } = useTheme();
  const snapPoints = useMemo(() => ["50%"], []);
  const sheetRef = useRef<BottomSheetModal>(null);
  const [selected, setSelected] = useState(null);
  const handleCategoryToggle = (categoryId: number) => {
    setSelected(categoryId);
  };

  const handleApply = () => {
    onApply(selected);
  };

  useEffect(() => {
    if (isVisible) {
      sheetRef.current?.present();
    } else {
      sheetRef.current?.dismiss();
    }
  }, [isVisible]);

  const handleDismiss = useCallback(() => {
    onDismiss?.();
  }, [onDismiss]);

  const renderBackdrop = useBottomSheetBackdrop();

  return (
    <StyledBottomSheetModal
      ref={sheetRef}
      index={0}
      snapPoints={snapPoints}
      enableDynamicSizing={false}
      onDismiss={handleDismiss}
      backdropComponent={renderBackdrop}
      backgroundStyle={{ backgroundColor: theme.colors.background }}
      enablePanDownToClose
      handleIndicatorStyle={{ backgroundColor: theme.colors.gray }}
    >
      <StyledBottomSheetView>
        <HeaderRow>
          <HeaderTitle>{title}</HeaderTitle>
          <CloseButton onPress={onDismiss}>
            <Ionicons name="close" size={24} color= {theme.colors.text} />
          </CloseButton>
        </HeaderRow>
        <Divider />

        <FilterContainer>
          {categoryList.map((item) => {
            const isSelected = item.id === selected;

            return (
              <FilterChip
                key={String(item.id)}
                active={isSelected}
                onPress={() => handleCategoryToggle(item.id)}
              >
                <FilterChipText active={isSelected}>
                  {item.title}
                </FilterChipText>
              </FilterChip>
            );
          })}
        </FilterContainer>

        <ButtonRow>
          <ClearButton
            onPress={() => {
              setSelected(null);
              onClear();
            }}
          >
            <ClearText>{clearText}</ClearText>
          </ClearButton>
          <ApplyButton onPress={handleApply}>
            <ApplyText>{applyText}</ApplyText>
          </ApplyButton>
        </ButtonRow>
      </StyledBottomSheetView>
    </StyledBottomSheetModal>
  );
};

export default FilterBottomSheet;
