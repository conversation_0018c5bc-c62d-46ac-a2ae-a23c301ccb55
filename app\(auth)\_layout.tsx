import { Stack } from "expo-router";
import { Redirect } from "expo-router";
import { RootState, useAppSelector } from "@/store/store";
import { useEffect } from "react";
import i18n from "../../i18n";
import { useTheme } from "@/hooks/useTheme";

export default function AuthLayout() {
  const token = useAppSelector((state: RootState) => state.auth.token);
  const language = useAppSelector(
    (state: RootState) => state.settings.language
  );
  const { theme } = useTheme();
  useEffect(() => {
    if (i18n.language !== language) {
      i18n.changeLanguage(language);
    }
  }, [language]);

  if (token) {
    return <Redirect href="/" />;
  }

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: theme.colors.background },
      }}
    >
      <Stack.Screen name="intro" />
      <Stack.Screen name="login" />
      <Stack.Screen name="register" />
      <Stack.Screen name="otp" />
    </Stack>
  );
}
