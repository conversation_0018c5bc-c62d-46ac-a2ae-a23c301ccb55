import { styled } from "@/utils/styled";
import { View, Text, Pressable } from "react-native";

export const Container = styled(View)`
  flex: 1;
`;

export const Form = styled(View)``;

export const WelcomeText = styled(Text)`
  font-size: 24px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
  margin-bottom: 8px;
`;

export const Subtitle = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  text-align: center;
  margin-bottom: 24px;
`;

export const ButtonRow = styled(View)`
  flex-direction: row;
  gap: 12px;
  align-items: center;
  margin-top: 20px;
`;

export const BackButton = styled(Pressable)`
  padding: 16px 24px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  background-color: transparent;
`;

export const BackButtonText = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
`;
