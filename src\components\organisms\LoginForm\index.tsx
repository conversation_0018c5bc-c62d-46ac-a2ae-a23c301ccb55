import React, { useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { Pressable } from "react-native";
import { useTranslation } from "react-i18next";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { getCountryListAction } from "@/store/actions/auth";
import { CountryCode } from "@/types/auth";
import { useTheme } from "@/hooks/useTheme";
import PhoneInput from "../../molecules/PhoneInput";
import Button from "../../atoms/Button";
import Text from "../../atoms/Text";
import {
  Container,
  WelcomeText,
  RegisterContainer,
  RegisterText,
  RegisterLink,
} from "./styles";
import Toast from "react-native-toast-message";

interface LoginFormProps {
  onSubmit: () => void;
  onRegisterPress: () => void;
  loading?: boolean;
}

export interface LoginFormData {
  phone: string;
}

const LoginForm: React.FC<LoginFormProps> = ({
  onSubmit,
  onRegisterPress,
  loading = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const { countryList } = useAppSelector((state) => state.auth);
  const {
    formState: { isValid },
  } = useFormContext();

  const [countryCode, setCountryCode] = useState<CountryCode | null>(
    countryList?.[0] || null
  );

  useEffect(() => {
    const fetchCountryList = async () => {
      if (!countryList) {
        try {
          const response = await dispatch(getCountryListAction({})).unwrap();
          if (response.status && response?.data) {
            setCountryCode(response?.data?.[0]);
          }
        } catch (error) {
          Toast.show({
            type: "error",
            text1: error?.message || "Failed to load country list",
          });
        }
      }
    };
    fetchCountryList();
  }, [dispatch, countryList]);

  const handleCountrySelect = (country: CountryCode) => {
    setCountryCode(country);
  };

  return (
    <Container>
      <WelcomeText>
        {t("welcome_back")} <Text style={{ fontSize: 22 }}>👋</Text>
      </WelcomeText>

      <PhoneInput
        label={t("phone_number")}
        name="phone"
        placeholder={t("phone_number")}
        countryCode={countryCode}
        onCountrySelect={handleCountrySelect}
        countryList={countryList || []}
      />

      <Button
        title={t("continue")}
        onPress={onSubmit}
        loading={loading}
        disabled={!isValid || loading}
      />

      <RegisterContainer>
        <RegisterText>{t("dont_have_an_account")}</RegisterText>
        <Pressable onPress={onRegisterPress}>
          <RegisterLink>{t("register")}</RegisterLink>
        </Pressable>
      </RegisterContainer>
    </Container>
  );
};

export default LoginForm;
