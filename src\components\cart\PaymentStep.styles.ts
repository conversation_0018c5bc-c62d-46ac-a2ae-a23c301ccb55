import { styled } from "@/utils/styled";
import { View, Text, Pressable, Image } from "react-native";

export const Container = styled(View)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const Title = styled(Text)`
  font-size: 24px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  margin: 20px 16px 16px 16px;
`;

export const PaymentMethodContainer = styled(View)`
  margin: 8px 16px;
  border-radius: 12px;
  background-color: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border};
  overflow: hidden;
`;

export const PaymentMethodHeader = styled(View)`
  padding: 16px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.border};
`;

export const PaymentMethodLabel = styled(Text)`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text};
`;

export const PaymentMethodContent = styled(View)`
  padding: 16px;
`;

export const PaymentDetailsContainer = styled(View)`
  gap: 12px;
`;

export const PaymentDetailRow = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
`;

export const PaymentDetailLabel = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-weight: 500;
`;

export const PaymentDetailValue = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  font-weight: 600;
  font-family: monospace;
`;

export const CopyButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
  background-color: ${({ theme }) => theme.colors.primary}10;
  border: 1px solid ${({ theme }) => theme.colors.primary};
  border-radius: 8px;
  margin-top: 8px;
`;

export const QRCodeContainer = styled(View)`
  align-items: center;
  gap: 16px;
`;

export const QRCodeImage = styled(Image)`
  width: 200px;
  height: 200px;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

export const DownloadButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 8px;
  min-width: 120px;
`;

export const DownloadButtonText = styled(Text)`
  color: white;
  font-weight: 600;
  margin-left: 8px;
`;

export const CheckboxContainer = styled(View)`
  flex-direction: row;
  align-items: center;
  gap: 12px;
`;

export const Checkbox = styled(Pressable)`
  width: 24px;
  height: 24px;
  border-radius: 4px;
  align-items: center;
  justify-content: center;
`;

export const CheckboxLabel = styled(Text)`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text};
  flex: 1;
`;

export const PaymentProofContainer = styled(View)`
  margin: 16px;
  padding: 16px;
  border-radius: 12px;
  background-color: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

export const PaymentProofLabel = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 12px;
`;

export const PaymentProofButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.primary}10;
  border: 2px dashed ${({ theme }) => theme.colors.primary};
  border-radius: 8px;
  gap: 8px;
`;

export const PaymentProofButtonText = styled(Text)`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.primary};
`;

export const PaymentProofPreview = styled(View)`
  align-items: center;
  gap: 12px;
`;

export const PaymentProofPreviewImage = styled(Image)`
  width: 200px;
  height: 150px;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

export const PaymentProofRemoveButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background-color: ${({ theme }) => theme.colors.error}10;
  border: 1px solid ${({ theme }) => theme.colors.error};
  border-radius: 6px;
  gap: 4px;
`;

export const PaymentProofRemoveButtonText = styled(Text)`
  font-size: 14px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.error};
`;
