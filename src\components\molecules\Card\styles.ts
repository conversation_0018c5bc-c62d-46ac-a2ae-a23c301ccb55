import { styled } from "@/utils/styled";
import { Pressable } from "react-native";

export const StyledCard = styled(Pressable)<{ 
  variant?: 'default' | 'elevated' | 'outlined' 
}>`
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  
  ${({ variant, theme }) => {
    switch (variant) {
      case 'elevated':
        return `
          shadow-color: ${theme.colors.shadow};
          shadow-offset: 0px 2px;
          shadow-opacity: 0.1;
          shadow-radius: 4px;
          elevation: 3;
        `;
      case 'outlined':
        return `
          border: 1px solid ${theme.colors.border};
        `;
      default:
        return '';
    }
  }}
`;
