import React, { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useAppDispatch, useAppSelector } from "@/store/store";
import {
  getCountryListAction,
  getStateListAction,
  getCityListAction,
} from "@/store/actions/auth";
import { CountryCode, StateList, CityList } from "@/types/auth";
import <PERSON><PERSON>ield from "../../molecules/FormField";
import PickerField from "../../molecules/PickerField";
import Button from "../../atoms/Button";
import { Container, SectionTitle, Form } from "./styles";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { PickerOption } from "@/components/molecules/PickerField/PickerBottomSheet";
import CountryItem from "@/components/atoms/CountryItem";
import { useTheme } from "@/hooks/useTheme";

interface ProfileEditFormProps {
  onSubmit: () => void;
  loading?: boolean;
}

export interface ProfileEditFormData {
  first_name: string;
  last_name: string;
  email: string;
  mobile: string;
  address_line_one: string;
  address_line_two: string;
  post_code: string;
  gst_number: string;
  country_id: number;
  state_id: number;
  city_id: number;
}

const ProfileEditForm: React.FC<ProfileEditFormProps> = ({
  onSubmit,
  loading = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const { countryList, stateList, cityList } = useAppSelector(
    (state) => state.auth
  );
  const {
    formState: { isValid },
    setValue,
    watch,
  } = useFormContext();
  const [selectedCountry, setSelectedCountry] = useState<CountryCode | null>(
    null
  );
  const [selectedState, setSelectedState] = useState<StateList | null>(null);
  const [selectedCity, setSelectedCity] = useState<CityList | null>(null);
  const countryId = watch("country_id");
  const stateId = watch("state_id");
  const cityId = watch("city_id");

  useEffect(() => {
    const fetchCountryList = async () => {
      if (!countryList?.length) {
        await dispatch(getCountryListAction({})).unwrap();
      }
    };
    fetchCountryList();
  }, [dispatch, countryList]);

  const handleCountrySelect = async (country: CountryCode) => {
    if (countryId === country.id) {
      setSelectedCountry(null);
      setValue("country_id", 0, { shouldValidate: true });
      setValue("state_id", 0);
      setValue("city_id", 0);
      setSelectedState(null);
      setSelectedCity(null);
      return false;
    }

    setSelectedCountry(country);
    setValue("country_id", country.id, { shouldValidate: true });
    setValue("state_id", 0);
    setValue("city_id", 0);
    setSelectedState(null);
    setSelectedCity(null);

    if (country.id && !stateList) {
      await dispatch(getStateListAction(country.id)).unwrap();
    }
    return true;
  };

  const handleStateSelect = async (state: StateList) => {
    if (stateId === state.id) {
      setSelectedState(null);
      setValue("state_id", 0, { shouldValidate: true });
      setValue("city_id", 0);
      setSelectedCity(null);
      return false;
    }

    setSelectedState(state);
    setValue("state_id", state.id, { shouldValidate: true });
    setValue("city_id", 0);
    setSelectedCity(null);

    if (state.id) {
      await dispatch(getCityListAction(state.id)).unwrap();
    }
    return true;
  };

  const handleCitySelect = (city: CityList) => {
    if (cityId === city.id) {
      setSelectedCity(null);
      setValue("city_id", 0, { shouldValidate: true });
      return false;
    }

    setSelectedCity(city);
    setValue("city_id", city.id, { shouldValidate: true });
    return true;
  };

  return (
    <Container>
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
        <Form>
          <SectionTitle>{t("profile.personalInfo")}</SectionTitle>

          <FormField
            name="first_name"
            label={t("profile.firstName")}
            placeholder={t("profile.enterFirstName")}
            placeholderTextColor={theme.colors.gray}
          />

          <FormField
            name="last_name"
            label={t("profile.lastName")}
            placeholder={t("profile.enterLastName")}
            placeholderTextColor={theme.colors.gray}
          />

          <FormField
            name="email"
            label={t("profile.email")}
            placeholder={t("profile.enterEmail")}
            placeholderTextColor={theme.colors.gray}
            keyboardType="email-address"
            autoCapitalize="none"
          />

          <FormField
            name="mobile"
            label={t("profile.mobile")}
            placeholder={t("profile.enterMobile")}
            placeholderTextColor={theme.colors.gray}
            keyboardType="phone-pad"
            maxLength={10}
          />

          <FormField
            name="address_line_one"
            label={t("profile.addressLineOne")}
            placeholder={t("profile.enterAddressLineOne")}
            placeholderTextColor={theme.colors.gray}
          />

          <FormField
            name="address_line_two"
            label={t("profile.addressLineTwo")}
            placeholder={t("profile.enterAddressLineTwo")}
            placeholderTextColor={theme.colors.gray}
          />

          <FormField
            name="post_code"
            label={t("profile.pincode")}
            placeholder={t("profile.enterPincode")}
            placeholderTextColor={theme.colors.gray}
            keyboardType="numeric"
            maxLength={6}
          />

          <PickerField
            name="country_id"
            label={t("profile.country")}
            placeholder={t("select_country")}
            options={countryList || []}
            onSelect={handleCountrySelect}
            displayKey="full_name"
            renderItem={(item: PickerOption) => (
              <CountryItem
                item={item}
                isSelected={countryId === item.id}
                displayKey="full_name"
              />
            )}
          />

          <PickerField
            name="state_id"
            label={t("profile.state")}
            placeholder={t("select_state")}
            options={stateList || []}
            onSelect={handleStateSelect}
            disabled={!countryId}
            displayKey="name"
            renderItem={(item: PickerOption) => (
              <CountryItem
                item={item}
                isSelected={stateId === item.id}
                displayKey="name"
              />
            )}
          />

          <PickerField
            name="city_id"
            label={t("profile.city")}
            placeholder={t("select_city")}
            options={cityList || []}
            onSelect={handleCitySelect}
            disabled={!stateId}
            displayKey="name"
            renderItem={(item: PickerOption) => (
              <CountryItem
                item={item}
                isSelected={cityId === item.id}
                displayKey="name"
              />
            )}
          />

          <FormField
            name="gst_number"
            label={t("profile.gstNumber")}
            placeholder={t("profile.enterGstNumber")}
            placeholderTextColor={theme.colors.gray}
            autoCapitalize="characters"
          />
        </Form>
      </KeyboardAwareScrollView>
      <Button
        title={t("profile.saveChanges")}
        onPress={onSubmit}
        loading={loading}
        disabled={!isValid || loading}
        style={{ marginTop: 12 }}
      />
    </Container>
  );
};

export default ProfileEditForm;
