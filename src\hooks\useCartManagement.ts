import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useAppDispatch } from "@/store/store";
import { useCart } from "@/hooks/useCart";
import {
  updateCartQuantity,
  clearProductFromCartAction,
} from "@/store/actions/order";
import { clearProductFromCartLocal } from "@/store/slices/orderSlice";
import { getDebouncedByKey } from "@/utils/debounceByKey";
import Toast from "react-native-toast-message";

export const useCartManagement = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { cartList, fetchCartList, updateQuantity, clearCart } = useCart();

  const handleQuantityChange = useCallback(
    (productId: number, newQuantity: number) => {
      // Update Redux store immediately for optimistic UI
      updateQuantity(productId, newQuantity);
      // Call debounced backend update function to reduce API calls
      const debouncedUpdate = getDebouncedByKey(
        productId,
        async (finalQuantity: number) => {
          try {
            await dispatch(
              updateCartQuantity({
                product_id: productId,
                quantity: finalQuantity,
              })
            ).unwrap();
          } catch (error: any) {
            Toast.show({
              type: "error",
              text1: error.message || "Failed to update quantity",
            });
          }
        }
      );

      debouncedUpdate(newQuantity);
    },
    [updateQuantity, dispatch]
  );

  const handleRemoveItem = useCallback(
    (itemId: number) => {
      const item = cartList?.cartItems.find((i) => i.product_id === itemId);

      if (item) {
        const payload = { product_id: itemId };

        // Update local state immediately
        dispatch(clearProductFromCartLocal(payload));

        // Update backend
        dispatch(clearProductFromCartAction(payload)).unwrap();
      }
    },
    [cartList?.cartItems, dispatch]
  );

  /**
   * Confirm and execute cart clearing
   */
  const confirmClearCart = useCallback(async () => {
    await clearCart();
  }, [clearCart]);

  return {
    // Data
    cartList,

    // Actions
    handleQuantityChange,
    handleRemoveItem,
    confirmClearCart,
    fetchCartList,
  };
};
