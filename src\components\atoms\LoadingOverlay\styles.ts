import { styled } from "@/utils/styled";
import { ActivityIndicator } from "react-native";

export const Container = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const StyledActivityIndicator = styled(ActivityIndicator).attrs(
  ({
    theme,
    size = "large",
  }: {
    theme: any;
    size?: "small" | "large" | number;
  }) => ({
    size,
    color: theme.colors.primary,
  })
)``;
