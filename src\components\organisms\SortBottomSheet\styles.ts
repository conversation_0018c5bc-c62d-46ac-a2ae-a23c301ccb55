import { styled } from "@/utils/styled";
import { BottomSheetModal } from "@gorhom/bottom-sheet";

export const StyledBottomSheetModal = styled(BottomSheetModal)`
  flex: 1;
`;

export const StyledBottomSheetView = styled.View`
  flex: 1;
`;

export const HeaderRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
`;

export const Title = styled.Text`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
`;

export const CloseButton = styled.Pressable`
  padding: 4px;
`;

export const Divider = styled.View`
  height: 1px;
  background-color: ${({ theme }) => theme.colors.border};
  margin-bottom: 8px;
`;

export const SortOptionsContainer = styled.View`
  flex: 1;
`;

export const SortOption = styled.Pressable<{ selected: boolean }>`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
`;

export const SortOptionText = styled.Text<{ selected: boolean }>`
  font-size: 16px;
  color: ${({ selected, theme }) =>
    selected ? theme.colors.text : theme.colors.gray};
`;

export const RadioButton = styled.View<{ selected: boolean }>`
  width: 20px;
  height: 20px;
  border-radius: 10px;
  border: 2px solid ${({ selected }) => (selected ? "#007AFF" : "#666")};
  justify-content: center;
  align-items: center;
`;

export const RadioButtonInner = styled.View<{ selected: boolean }>`
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: ${({ selected }) => (selected ? "#007AFF" : "transparent")};
`;

export const ButtonRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background};
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.border};
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  gap: 16px;
`;

export const ClearButton = styled.Pressable`
  padding: 12px 24px;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.white};
  text-align: center;
  justify-content: center;
`;

export const ClearText = styled.Text`
  font-weight: bold;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.primary};
  text-align: center;
  justify-content: center;
`;

export const ApplyButton = styled.Pressable`
  padding: 12px 24px;
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 8px;
  flex: 1;
  text-align: center;
  justify-content: center;
`;

export const ApplyText = styled.Text`
  color: ${({ theme }) => theme.colors.white};
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  justify-content: center;
`;
