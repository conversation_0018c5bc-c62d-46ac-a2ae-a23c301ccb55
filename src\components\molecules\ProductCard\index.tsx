import React from "react";
import { useTranslation } from "react-i18next";
import { ProductType } from "@/types/product";
import Text from "../../atoms/Text";
import Button from "../../atoms/Button";
import {
  Container,
  ProductImage,
  ProductInfo,
  ProductName,
  ProductPrice,
  ProductDescription,
  AddToCartButton,
  StyledProductImage,
} from "./styles";

interface ProductCardProps {
  product: ProductType;
  onPress?: () => void;
  onAddToCart?: () => void;
  showAddToCart?: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onPress,
  onAddToCart,
  showAddToCart = true,
}) => {
  const { t } = useTranslation();

  return (
    <Container onPress={onPress}>
      <ProductImage>
        <StyledProductImage
          source={{ uri: product.main_image?.url }}
          contentFit="cover"
        />
      </ProductImage>

      <ProductInfo>
        <ProductName numberOfLines={2}>{product.title}</ProductName>

        <ProductPrice>₹{product.price}</ProductPrice>

        {product.description && (
          <ProductDescription numberOfLines={2}>
            {product.description}
          </ProductDescription>
        )}

        {showAddToCart && (
          <AddToCartButton>
            <Button
              title={t("product.add_to_cart")}
              onPress={onAddToCart}
              variant="primary"
            />
          </AddToCartButton>
        )}
      </ProductInfo>
    </Container>
  );
};

export default ProductCard;
