import { styled } from "@/utils/styled";
import { View } from "react-native";

export const Container = styled(View)`
  background-color: ${({ theme }) => theme.colors.card};
  padding: 20px;
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.border};
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px -2px;
  shadow-opacity: 0.1;
  shadow-radius: 8px;
  elevation: 8;
`;

export const ButtonContainer = styled(View)`
  flex-direction: row;
  gap: 12px;
  align-items: center;
`;
