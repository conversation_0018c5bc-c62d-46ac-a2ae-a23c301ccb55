import React from "react";
import { ViewStyle } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { CheckboxContainer, CheckboxCircle, CheckboxLabel } from "./styles";
import { useTheme } from "@/hooks/useTheme";

interface CheckboxProps {
  checked: boolean;
  onPress: () => void;
  label?: string;
  disabled?: boolean;
  style?: ViewStyle;
}

const Checkbox: React.FC<CheckboxProps> = ({
  checked,
  onPress,
  label,
  disabled = false,
  style,
}) => {
  const { theme } = useTheme();

  return (
    <CheckboxContainer onPress={onPress} disabled={disabled} style={style}>
      <CheckboxCircle
        checked={checked}
        disabled={disabled}
        style={{
          backgroundColor: checked ? theme.colors.primary : "transparent",
          borderColor: theme.colors.primary,
        }}
      >
        {checked && <Ionicons name="checkmark" size={16} color="white" />}
      </CheckboxCircle>
      {label && (
        <CheckboxLabel checked={checked} disabled={disabled}>
          {label}
        </CheckboxLabel>
      )}
    </CheckboxContainer>
  );
};

export default Checkbox;
