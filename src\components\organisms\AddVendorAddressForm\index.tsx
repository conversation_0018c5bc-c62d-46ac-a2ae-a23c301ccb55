import React, { useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { getStateListAction, getCityListAction } from "@/store/actions/auth";
import { StateList, CityList } from "@/types/auth";
import FormField from "../../molecules/FormField";
import PickerField from "../../molecules/PickerField";
import Button from "../../atoms/Button";
import {
  Container,
  Form,
  ButtonRow,
  BackButton,
  BackButtonText,
} from "./styles";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { PickerOption } from "@/components/molecules/PickerField/PickerBottomSheet";
import CountryItem from "@/components/atoms/CountryItem";
import { useTheme } from "@/hooks/useTheme";

interface AddVendorAddressFormFormProps {
  onSubmit: () => void;
  onBack: (formData?: AddVendorAddressFormFormData) => void;
  loading?: boolean;
  selectedCountryId: number | null;
}

export interface AddVendorAddressFormFormData {
  address_line_one: string;
  address_line_two: string;
  post_code: string;
  state_id: number;
  city_id: number;
}

const AddVendorAddressFormForm: React.FC<AddVendorAddressFormFormProps> = ({
  onSubmit,
  onBack,
  loading = false,
  selectedCountryId,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const { stateList, cityList, isStateListLoading, isCityListLoading } =
    useAppSelector((state) => state.auth);

  const {
    formState: { isValid },
    setValue,
    trigger,
    watch,
  } = useFormContext();
  const [selectedStateId, setSelectedStateId] = useState<number | null>(null);
  const [selectedCity, setSelectedCity] = useState<CityList | null>(null);

  const formValues = watch();
  useEffect(() => {
    const hasExistingData =
      formValues.address_line_one ||
      formValues.address_line_two ||
      formValues.post_code ||
      formValues.state_id ||
      formValues.city_id;

    if (hasExistingData) {
      trigger();
    }
  }, []); // Only run on mount

  useEffect(() => {
    if (selectedCountryId && !stateList) {
      dispatch(getStateListAction(selectedCountryId));
    }
  }, [selectedCountryId, dispatch, stateList]);

  // Set default values when form loads with existing data
  useEffect(() => {
    if (formValues.state_id && formValues.state_id > 0) {
      setSelectedStateId(formValues.state_id);
      // Load cities if state is already selected
      if (formValues.state_id && !cityList) {
        dispatch(getCityListAction(formValues.state_id));
      }
    }
  }, [formValues.state_id, cityList, dispatch]);

  const handleStateSelect = async (state: StateList) => {
    if (formValues.state_id === state.id) {
      setValue("state_id", 0, { shouldValidate: true });
      setValue("city_id", 0);
      setSelectedStateId(0);
      setSelectedCity(null);
      return false;
    }

    setValue("state_id", state.id, { shouldValidate: true });
    setValue("city_id", 0);
    setSelectedStateId(state.id);
    setSelectedCity(null);

    if (state.id) {
      await dispatch(getCityListAction(state.id)).unwrap();
    }
    return true;
  };

  const handleCitySelect = (city: CityList) => {
    if (formValues.city_id === city.id) {
      setSelectedCity(null);
      setValue("city_id", 0, { shouldValidate: true });
      return false;
    }

    setSelectedCity(city);
    setValue("city_id", city.id, { shouldValidate: true });
    return true;
  };

  const handleBackPress = () => {
    const currentFormData: AddVendorAddressFormFormData = {
      address_line_one: formValues.address_line_one || "",
      address_line_two: formValues.address_line_two || "",
      post_code: formValues.post_code || "",
      state_id: formValues.state_id || 0,
      city_id: formValues.city_id || 0,
    };
    onBack(currentFormData);
  };

  return (
    <Container>
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
        <Form>
          <FormField
            name="address_line_one"
            placeholder={t("address_line_one")}
            placeholderTextColor={theme.colors.gray}
            label={t("address_line_one")}
          />

          <FormField
            name="address_line_two"
            placeholder={t("address_line_two")}
            placeholderTextColor={theme.colors.gray}
            label={t("address_line_two")}
          />

          <FormField
            name="post_code"
            placeholder={t("post_code")}
            placeholderTextColor={theme.colors.gray}
            keyboardType="numeric"
            maxLength={6}
            label={t("post_code")}
          />

          <PickerField
            name="state_id"
            placeholder={t("select_state")}
            options={stateList || []}
            onSelect={handleStateSelect}
            disabled={!selectedCountryId}
            displayKey="name"
            isLoading={isStateListLoading}
            label={t("select_state")}
            renderItem={(item: PickerOption) => (
              <CountryItem
                item={item}
                isSelected={formValues.state_id === item.id}
                displayKey="name"
              />
            )}
          />

          <PickerField
            name="city_id"
            placeholder={t("select_city")}
            options={cityList || []}
            onSelect={handleCitySelect}
            disabled={!selectedStateId}
            displayKey="name"
            isLoading={isCityListLoading}
            label={t("select_city")}
            renderItem={(item: PickerOption) => (
              <CountryItem
                item={item}
                isSelected={formValues.city_id === item.id}
                displayKey="name"
              />
            )}
          />
        </Form>
      </KeyboardAwareScrollView>
      <ButtonRow>
        <BackButton onPress={handleBackPress}>
          <BackButtonText>{t("back")}</BackButtonText>
        </BackButton>

        <Button
          title={t("complete_profile")}
          onPress={onSubmit}
          loading={loading}
          disabled={!isValid || loading}
          style={{ flex: 1 }}
        />
      </ButtonRow>
    </Container>
  );
};

export default AddVendorAddressFormForm;
