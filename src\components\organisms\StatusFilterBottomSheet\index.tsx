import React, { use<PERSON><PERSON><PERSON>, useMemo, useState } from "react";
import { BottomSheetModal, BottomSheetScrollView } from "@gorhom/bottom-sheet";
import { useTranslation } from "react-i18next";
import { useBottomSheetBackdrop } from "@/utils/commonBackdrop";
import {
  Title,
  OptionsContainer,
  OptionButton,
  OptionText,
  ClearText,
  ApplyText,
  ButtonRow,
  ClearButton,
  ApplyButton,
  StyledBottomSheetView,
  HeaderRow,
  CloseButton,
  Divider,
  OptionButtonsContainer,
} from "./styles";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import { Spacer } from "@/styles/common.style";
import { useFocusEffect } from "expo-router";

export interface StatusOption {
  id: number | null;
  label: string;
}

interface StatusFilterBottomSheetProps {
  isVisible: boolean;
  onDismiss: () => void;
  statusOptions: StatusOption[];
  orderTypeOptions?: StatusOption[];
  onClear: () => void;
  onApply: (statusId: number | null, orderTypeId: number | null) => void;
  filters: any;
  selectedkey?: string;
}

const StatusFilterBottomSheet: React.FC<StatusFilterBottomSheetProps> = ({
  isVisible,
  onDismiss,
  statusOptions,
  orderTypeOptions,
  onClear,
  onApply,
  filters,
  selectedkey,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const bottomSheetRef = React.useRef<BottomSheetModal>(null);
  const [selectedStatus, setSelectedStatus] = useState<number | null>(null);
  const [selectedOrderType, setSelectedOrderType] = useState<number | null>(
    null
  );
  const snapPoints = useMemo(() => ["80%"], []);

  useFocusEffect(
    useCallback(() => {
      if (isVisible) {
        setSelectedStatus(filters[selectedkey ? selectedkey : "status"]);
        setSelectedOrderType(filters?.order_type || null);
        bottomSheetRef.current?.present();
      } else {
        bottomSheetRef.current?.dismiss();
      }
    }, [isVisible, filters])
  );

  const renderBackdrop = useBottomSheetBackdrop();

  const handleClear = () => {
    setSelectedStatus(null);
    setSelectedOrderType(null);
    onClear();
  };

  const handleApply = () => {
    onApply(selectedStatus, selectedOrderType);
  };

  return (
    <BottomSheetModal
      ref={bottomSheetRef}
      index={0}
      snapPoints={snapPoints}
      enableDynamicSizing={false}
      enablePanDownToClose
      onDismiss={onDismiss}
      backgroundStyle={{ backgroundColor: theme.colors.background }}
      backdropComponent={renderBackdrop}
      handleIndicatorStyle={{ backgroundColor: theme.colors.gray }}
    >
      <StyledBottomSheetView>
        <HeaderRow>
          <Title>{t("Filter")}</Title>
          <CloseButton onPress={onDismiss}>
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </CloseButton>
        </HeaderRow>
        <Divider />
        <BottomSheetScrollView>
          <OptionsContainer>
            {statusOptions && statusOptions.length > 0 && (
              <>
                <Title>{t("Status")}</Title>
                <Spacer size={16} />
                <OptionButtonsContainer>
                  {statusOptions.map((option) => (
                    <OptionButton
                      key={option.id}
                      onPress={() =>
                        setSelectedStatus(
                          selectedStatus === option.id ? null : option.id
                        )
                      }
                      isSelected={selectedStatus === option.id}
                    >
                      <OptionText isSelected={selectedStatus === option.id}>
                        {option.label}
                      </OptionText>
                    </OptionButton>
                  ))}
                </OptionButtonsContainer>
              </>
            )}
            <Spacer size={24} />
            {orderTypeOptions && orderTypeOptions.length > 0 && (
              <>
                <Title>{t("order.ordertype")}</Title>
                <Spacer size={16} />
                <OptionButtonsContainer>
                  {orderTypeOptions.map((option) => (
                    <OptionButton
                      key={`2-${option.id}`}
                      onPress={() => {
                        setSelectedOrderType(
                          selectedOrderType === option.id ? null : option.id
                        );
                      }}
                      isSelected={selectedOrderType === option.id}
                    >
                      <OptionText isSelected={selectedOrderType === option.id}>
                        {option.label}
                      </OptionText>
                    </OptionButton>
                  ))}
                </OptionButtonsContainer>
              </>
            )}
          </OptionsContainer>
        </BottomSheetScrollView>
        <ButtonRow>
          <ClearButton onPress={handleClear}>
            <ClearText>{t("clear.all")}</ClearText>
          </ClearButton>
          <ApplyButton onPress={handleApply}>
            <ApplyText>{t("filter.apply")}</ApplyText>
          </ApplyButton>
        </ButtonRow>
      </StyledBottomSheetView>
    </BottomSheetModal>
  );
};

export default StatusFilterBottomSheet;
