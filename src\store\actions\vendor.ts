import { createAsyncThunk } from "@reduxjs/toolkit";
import {
  VendorInsertUpdatePayloadType,
  VendorListPayloadType,
  VendorListResponse,
} from "../../types/vendor";
import api from "../../services/api";
import { ApiResponse } from "../../types/api";

import { withToastForError } from "../../utils/thunk";

export const getVendorListAction = createAsyncThunk(
  "vendor/getVendorListAction",
  withToastForError(
    async (
      payload: VendorListPayloadType
    ): Promise<VendorListResponse & ApiResponse> => {
      const response = await api.get("/vendor-list", { params: payload });
      return {
        ...response.data,
        meta: payload,
      };
    }
  )
);

export const vendorInsertUpdateAction = createAsyncThunk(
  "vendor/vendorInsertUpdateAction",
  withToastForError(
    async (payload: VendorInsertUpdatePayloadType): Promise<ApiResponse> => {
      const response = await api.post(`/vendor-insert-update`, payload);
      return response.data;
    }
  )
);

export const deleteVendorAction = createAsyncThunk(
  "vendor/deleteVendorAction",
  withToastForError(
    async (payload: { vendor_id: number }): Promise<ApiResponse> => {
      const response = await api.delete(`/vendor-delete`, { data: payload });
      return response.data;
    }
  )
);
