import React from "react";
import {
  useForm,
  FormProvider,
  <PERSON><PERSON>ultV<PERSON><PERSON>,
  SubmitHandler,
} from "react-hook-form";

type FormTemplateProps<TFormValues extends Record<string, any>> = {
  Component: React.ComponentType<any>;
  onSubmit: SubmitHandler<TFormValues>;
  onCancel?: () => void;
  defaultValues?: DefaultValues<TFormValues>;
  mode?: "onChange" | "onBlur" | "onSubmit" | "all";
  resolver?: any;
};

function FormTemplate<TFormValues extends Record<string, any>>({
  Component,
  onSubmit,
  onCancel,
  defaultValues,
  mode = "onChange",
  resolver,
}: FormTemplateProps<TFormValues>) {
  const methods = useForm<TFormValues>({
    defaultValues,
    mode,
    resolver,
  });

  return (
    <FormProvider {...methods}>
      <Component {...methods} onSubmit={methods.handleSubmit(onSubmit)} onCancel={onCancel} />
    </FormProvider>
  );
}

export default FormTemplate;
