import React from "react";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import { OrderDetail } from "@/types/order";
import { SectionHeader } from "@/components/atoms";
import { ProductItemCard } from "@/components/molecules";
import { Card } from "@/components/molecules";

interface OrderItemsProps {
  orderDetails: OrderDetail[];
}

/**
 * OrderItems - Organism component for order items section
 * Displays list of products in the order with details
 */
const OrderItems: React.FC<OrderItemsProps> = ({ orderDetails }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  return (
    <Card variant="elevated">
      <SectionHeader
        title={t("order.products")}
        subtitle={`${orderDetails.length} ${t("items")}`}
        icon={
          <Ionicons name="bag-outline" size={20} color={theme.colors.primary} />
        }
      />

      {orderDetails.map((item, index) => (
        <ProductItemCard
          key={index}
          productName={item?.product?.title || ""}
          productImage={item?.product?.main_image?.url || ""}
          quantity={item.quantity}
          price={item.price}
          subtotal={item.total_amount}
        />
      ))}
    </Card>
  );
};

export default OrderItems;
