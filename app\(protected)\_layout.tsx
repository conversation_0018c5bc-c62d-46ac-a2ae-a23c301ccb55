import { Stack } from "expo-router";
import { Redirect } from "expo-router";
import { RootState, useAppSelector } from "@/store/store";
import { useTheme } from "@/hooks/useTheme";

export default function ProtectedLayout() {
  const { token, user } = useAppSelector((state: RootState) => state.auth);
  const { theme } = useTheme();

  if (!token) {
    return <Redirect href="/(auth)/login" />;
  }

  if (user?.is_profile_complete === 0) {
    return <Redirect href="/profile-setup" />;
  }

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: theme.colors.background },
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name="(tabs)"
        options={{
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="profile/index"
        options={{
          headerShown: false,
          presentation: "card",
        }}
      />

      <Stack.Screen
        name="profile/edit"
        options={{
          headerShown: false,
          presentation: "card",
        }}
      />

      <Stack.Screen
        name="cart/index"
        options={{
          headerShown: false,
          presentation: "card",
        }}
      />

      <Stack.Screen
        name="add-address/index"
        options={{
          headerShown: false,
          presentation: "card",
        }}
      />

      <Stack.Screen
        name="vendor-management/index"
        options={{
          headerShown: false,
          presentation: "card",
        }}
      />

      <Stack.Screen
        name="addvendor/index"
        options={{
          headerShown: false,
          presentation: "card",
        }}
      />

      <Stack.Screen
        name="order/[id]"
        options={{
          headerShown: false,
          presentation: "card",
        }}
      />

      <Stack.Screen
        name="product/[slug]"
        options={{
          headerShown: false,
          presentation: "card",
        }}
      />

      <Stack.Screen
        name="complaint/[id]"
        options={{
          headerShown: false,
          presentation: "card",
        }}
      />

      <Stack.Screen
        name="complaint/new"
        options={{
          headerShown: false,
          presentation: "card",
        }}
      />
    </Stack>
  );
}
