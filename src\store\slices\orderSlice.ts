import { createSlice } from "@reduxjs/toolkit";
import { OrderState } from "../../types/order";
import {
  checkCouponCodeAction,
  getCartListAction,
  getOrderDetailsAction,
  getOrdersListAction,
  getOrderStatusListAction,
  orderStatusChangeAction,
  placeOrderAction,
} from "../actions/order";
import { gstCalculation } from "../../utils/common";
import { logoutAction } from "../actions/auth";
import { logoutLocalAction } from "../actions/auth";

const initialState: OrderState = {
  cartList: null,
  orderDetails: null,
  cartLoading: false,
  cartError: null,
  addToCartLoading: false,
  addToCartError: null,
  orders: null,
  ordersLoading: false,
  ordersError: null,
  orderStatusList: null,
  couponCode: null,
  current_page: 1,
  last_page: 1,
  loadedPages: [],
  filters: null,
  selectedPaymentMethod: null,
  paymentProof: null,
};

const orderSlice = createSlice({
  name: "order",
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = action.payload;
    },
    updateCartItemQuantityLocal: (state, action) => {
      const { id, quantity } = action.payload;

      // Update quantity of the specific cart item
      const item = state.cartList?.cartItems?.find((i) => i.product_id === id);
      if (item) {
        item.quantity = quantity;
      }

      // Call GST calculation function with updated cart list
      const gst = gstCalculation({
        cartItems: state.cartList.cartItems,
        cgst_percentage: state.cartList.cgst_percentage,
        sgst_percentage: state.cartList.sgst_percentage,
        igst_percentage: state.cartList.igst_percentage,
      });

      // Merge the calculated values into state
      state.cartList = {
        ...state.cartList,
        ...gst,
      };
    },
    clearProductFromCartLocal: (state, action) => {
      const { product_id } = action.payload;

      if (state.cartList) {
        // Remove the product from the cart
        state.cartList.cartItems = state.cartList.cartItems?.filter(
          (item) => item.product_id !== product_id
        );

        // If there are still items in the cart, recalculate GST
        if (state.cartList.cartItems.length > 0) {
          const gst = gstCalculation({
            cartItems: state.cartList.cartItems,
            cgst_percentage: state.cartList.cgst_percentage,
            sgst_percentage: state.cartList.sgst_percentage,
            igst_percentage: state.cartList.igst_percentage,
          });

          // Merge the GST values into state
          state.cartList = {
            ...state.cartList,
            ...gst,
          };
        }
      }
    },
    clearCartLocal: (state) => {
      state.cartList = null;
      state.selectedPaymentMethod = null;
      state.paymentProof = null;
    },
    setSelectedPaymentMethod: (state, action) => {
      state.selectedPaymentMethod = action.payload;
      // Clear payment proof when switching methods
      state.paymentProof = null;
    },
    setPaymentProof: (state, action) => {
      state.paymentProof = action.payload;
    },
    clearPaymentProof: (state) => {
      state.paymentProof = null;
    },
    clearPaymentState: (state) => {
      state.selectedPaymentMethod = null;
      state.paymentProof = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all orders
      .addCase(getCartListAction.pending, (state) => {
        state.cartLoading = true;
        state.cartError = null;
      })
      .addCase(getCartListAction.fulfilled, (state, action) => {
        state.cartLoading = false;
        const gst = gstCalculation(action.payload.data);
        state.cartList = {
          ...action.payload.data,
          ...gst,
        };
      })
      .addCase(getCartListAction.rejected, (state, action) => {
        state.cartLoading = false;
        state.cartError = action.error.message || "Failed to load cart list";
      })
      // Get all orders
      .addCase(getOrdersListAction.pending, (state) => {
        state.ordersLoading = true;
        state.ordersError = null;
      })
      .addCase(getOrdersListAction.fulfilled, (state, action) => {
        state.ordersLoading = false;
        state.filters = action.payload.meta;
        const responseData = action.payload?.data;

        if (responseData) {
          state.current_page = responseData.current_page;
          state.last_page = responseData.last_page;

          if (!state.loadedPages.includes(responseData.current_page)) {
            state.loadedPages.push(responseData.current_page);
          }

          if (responseData.current_page === 1) {
            state.orders = responseData.data;
            state.loadedPages = [1];
          } else {
            state.orders = [...state.orders, ...responseData.data];
          }
        }
      })
      .addCase(getOrdersListAction.rejected, (state, action) => {
        state.ordersLoading = false;
        state.ordersError = action.error.message || "Failed to load orders";
      })
      // Get order details
      .addCase(getOrderDetailsAction.fulfilled, (state, action) => {
        state.orderDetails = action.payload.data;
      })
      .addCase(checkCouponCodeAction.fulfilled, (state, action) => {
        state.couponCode = action.payload.coupon_code;
      })
      .addCase(checkCouponCodeAction.rejected, (state, action) => {
        state.couponCode = null;
      })
      .addCase(placeOrderAction.fulfilled, (state, action) => {
        state.couponCode = null;
        state.cartList = null;
        state.selectedPaymentMethod = null;
        state.paymentProof = null;
      })
      .addCase(placeOrderAction.rejected, (state, action) => {
        state.couponCode = null;
      })
      // Get order status list
      .addCase(getOrderStatusListAction.fulfilled, (state, action) => {
        state.orderStatusList = action.payload.data;
      })
      // Order status change
      .addCase(orderStatusChangeAction.fulfilled, (state, action) => {
        if (state.orderDetails) {
          state.orderDetails.status = action.meta.arg.status;
        }
      })
      // Logout
      .addCase(logoutAction.fulfilled, () => {
        return initialState;
      })
      // Logout Local
      .addCase(logoutLocalAction.fulfilled, () => {
        return initialState;
      });
  },
});

export const {
  updateCartItemQuantityLocal,
  clearProductFromCartLocal,
  clearCartLocal,
  setFilters,
  setSelectedPaymentMethod,
  setPaymentProof,
  clearPaymentProof,
  clearPaymentState,
} = orderSlice.actions;
export default orderSlice.reducer;
