export enum ComplaintStatusEnum {
  Pending = 1,
  InReview = 2,
  EstimationSent = 3,
  Approved = 4,
  RepairInProgress = 5,
  Resolved = 6,
  Rejected = 7,
}

export interface Complaint {
  id: string;
  status: ComplaintStatusEnum;
  description: string;
  serial_number: string;
  created_at: string;
  updated_at?: string;
  resolution?: string;
  resolved_at?: string;
  image?: string;
  warranty_status?: "in_warranty" | "out_of_warranty" | "partial_warranty";
  repair_cost?: number;
}

export interface ComplaintResponse {
  message: string;
  status: boolean;
  data: Complaint | Complaint[];
}

export interface CreateComplaintPayload {
  description: string;
  serial_number: string;
}

export interface UpdateComplaintPayload {
  id: string;
  status?: ComplaintStatusEnum;
  description?: string;
  resolution?: string;
}

export interface ResolveComplaintPayload {
  id: string;
  resolution: string;
}

export interface ComplaintState {
  complaints: Complaint[];
  complaintDetails: Complaint | null;
  loading: boolean;
  error: string | null;
}
