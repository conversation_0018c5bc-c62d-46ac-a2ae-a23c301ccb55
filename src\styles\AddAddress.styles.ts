import { styled } from "../utils/styled";
import { View, Text, TextInput, Pressable } from "react-native";

export const Container = styled(View)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const Form = styled(View)`
  flex: 1;
`;

export const InputGroup = styled(View)`
  margin-bottom: 20px;
`;

export const Label = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: ${({ theme }) => theme.colors.text};
`;

export const Input = styled(TextInput)`
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 16px;
  background-color: ${({ theme }) => theme.colors.card};
  color: ${({ theme }) => theme.colors.text};
`;

export const PickerButton = styled(Pressable)`
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  padding: 12px 16px;
  background-color: ${({ theme }) => theme.colors.card};
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

export const PickerText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  flex: 1;
`;

export const DisabledPickerButton = styled(View)`
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  padding: 12px 16px;
  background-color: ${({ theme }) => theme.colors.gray};
  opacity: 0.6;
`;

export const SaveButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 8px;
  padding: 16px;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 40px;
`;

export const SaveButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 16px;
  font-weight: 600;
`;

export const RadioGroup = styled(View)`
  flex-direction: row;
  gap: 20px;
`;

export const RadioOption = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  gap: 8px;
`;

export const RadioButton = styled(View)<{ selected: boolean }>`
  width: 24px;
  height: 24px;
  border-radius: 12px;
  border-width: 2px;
  border-color: ${({ theme, selected }) =>
    selected ? theme.colors.primary : theme.colors.border};
  background-color: ${({ theme, selected }) =>
    selected ? theme.colors.primary : "transparent"};
  align-items: center;
  justify-content: center;
`;

export const RadioLabel = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;
