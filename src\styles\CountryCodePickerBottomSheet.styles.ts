import { FlatList, TextInput } from "react-native";
import { styled } from "../utils/styled";
import { Image } from "expo-image";

export const Title = styled.Text`
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: ${({ theme }) => theme.colors.primary};
`;

export const SearchInput = styled(TextInput)`
  height: 40px;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.lightGray};
  padding: 0 16px;
  color: ${({ theme }) => theme.colors.text};
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.lightGray};
`;

export const ListContainer = styled(FlatList)``;

export const CountryItem = styled.Pressable`
  flex-direction: row;
  align-items: center;
  padding: 16px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.lightGray};
`;

export const FlagContainer = styled.View`
  width: 32px;
  height: 24px;
  margin-right: 8px;
  justify-content: center;
  align-items: center;
`;

export const FlagPlaceholderImage = styled(Image)`
  width: 32px;
  height: 24px;
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.gray};
`;

export const CountryName = styled.Text`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-right: 8px;
`;

export const CountryCode = styled.Text`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
