import React, { useState } from "react";
import { ActivityIndicator } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import {
  FileUploader<PERSON>ontainer,
  FileUploaderLabel,
  FileUploaderButton,
  FileUploaderButtonText,
  FileUploaderPreview,
  FileUploaderPreviewImage,
  FileUploaderRemoveButton,
  FileUploaderRemoveButtonText,
  FileInfoText,
} from "./styles";
import { useTheme } from "@/hooks/useTheme";
import Toast from "react-native-toast-message";
import ImagePickerModal from "../ImagePickerModal";
import { useFileValidation } from "@/hooks/useFileValidation";

interface FileUploaderProps {
  file: ImagePicker.ImagePickerAsset | null;
  onUpload: (asset: ImagePicker.ImagePickerAsset) => void;
  onRemove: () => void;
  label: string;
  required?: boolean;
  fileType?: "image" | "general";
  showPreview?: boolean;
}

const FileUploader: React.FC<FileUploaderProps> = ({
  file,
  onUpload,
  onRemove,
  label,
  required = false,
  fileType = "image",
  showPreview = true,
}) => {
  const { theme } = useTheme();
  const { validateImageFile, getSupportedFormatsDisplay, getMaxFileSize } =
    useFileValidation();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const [loadingType, setLoadingType] = useState<'camera' | 'gallery' | null>(null);

  const validateAndUpload = async (asset: ImagePicker.ImagePickerAsset) => {
    setIsUploading(true);
    try {
      const validation = await validateImageFile(asset);

      if (validation.isValid) {
        onUpload(asset);
        Toast.show({
          type: "success",
          text1: `${label} uploaded successfully!`,
        });
        setIsModalVisible(false);
      } else {
        Toast.show({
          type: "error",
          text1: validation.error || "File validation failed",
        });
      }
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "Failed to validate file",
      });
    } finally {
      setIsUploading(false);
      setLoadingType(null);
    }
  };

  const handleCameraPress = async () => {
    try {
      setLoadingType('camera');
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== "granted") {
        Toast.show({
          type: "error",
          text1: "Camera permission is required!",
        });
        setLoadingType(null);
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await validateAndUpload(result.assets[0]);
      } else {
        setLoadingType(null);
      }
    } catch (error) {
      console.error("Camera error:", error);
      Toast.show({
        type: "error",
        text1: "Failed to capture file",
      });
      setLoadingType(null);
    }
  };

  const handleGalleryPress = async () => {
    try {
      setLoadingType('gallery');
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== "granted") {
        Toast.show({
          type: "error",
          text1: "Permission to access media library is required!",
        });
        setLoadingType(null);
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes:
          fileType === "image"
            ? ImagePicker.MediaTypeOptions.Images
            : ImagePicker.MediaTypeOptions.All,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await validateAndUpload(result.assets[0]);
      } else {
        setLoadingType(null);
      }
    } catch (error) {
      console.error("Gallery error:", error);
      Toast.show({
        type: "error",
        text1: "Failed to select file",
      });
      setLoadingType(null);
    }
  };

  const handleUploadFile = () => {
    setIsModalVisible(true);
  };

  const handleRemove = async () => {
    setIsRemoving(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      onRemove();
      Toast.show({
        type: "info",
        text1: `${label} removed`,
      });
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "Failed to remove file",
      });
    } finally {
      setIsRemoving(false);
    }
  };

  const getFileIcon = () => {
    if (fileType === "image") {
      return "camera-outline";
    }
    return "document-outline";
  };

  const getButtonText = () => {
    if (fileType === "image") {
      return "Upload Image";
    }
    return "Upload File";
  };

  return (
    <>
      <FileUploaderContainer>
        <FileUploaderLabel>
          {label} {required && "(Required)"}
        </FileUploaderLabel>

        {!file ? (
          <FileUploaderButton
            onPress={handleUploadFile}
            disabled={isUploading}
            style={{ opacity: isUploading ? 0.6 : 1 }}
          >
            <Ionicons
              name={isUploading ? "hourglass-outline" : getFileIcon()}
              size={20}
              color={theme.colors.primary}
            />
            <FileUploaderButtonText>
              {isUploading ? "Uploading..." : getButtonText()}
            </FileUploaderButtonText>
          </FileUploaderButton>
        ) : (
          <FileUploaderPreview>
            {showPreview && file.type?.startsWith("image/") && (
              <FileUploaderPreviewImage source={{ uri: file.uri }} />
            )}
            <FileInfoText>
              {file.fileName || file.uri.split("/").pop()}
            </FileInfoText>
            <FileUploaderRemoveButton
              onPress={handleRemove}
              disabled={isRemoving}
              style={{ opacity: isRemoving ? 0.6 : 1 }}
            >
              {isRemoving ? (
                <ActivityIndicator
                  size={16}
                  color={theme.colors.white}
                />
              ) : (
                <Ionicons
                  name="trash-outline"
                  size={16}
                  color={theme.colors.white}
                />
              )}
              <FileUploaderRemoveButtonText>
                {isRemoving ? "Removing..." : "Remove"}
              </FileUploaderRemoveButtonText>
            </FileUploaderRemoveButton>
          </FileUploaderPreview>
        )}

        {/* File requirements info */}
        <FileInfoText style={{ fontSize: 12, marginTop: 8, opacity: 0.7 }}>
          Supported formats: {getSupportedFormatsDisplay(fileType)}
        </FileInfoText>
        <FileInfoText style={{ fontSize: 12, opacity: 0.7 }}>
          Max size: {getMaxFileSize(fileType)}KB
        </FileInfoText>
      </FileUploaderContainer>

      <ImagePickerModal
        visible={isModalVisible}
        onClose={() => !isUploading && setIsModalVisible(false)}
        onCameraPress={handleCameraPress}
        onGalleryPress={handleGalleryPress}
        title={`Upload ${label}`}
        isLoading={isUploading}
        loadingType={loadingType}
      />
    </>
  );
};

export default FileUploader;
