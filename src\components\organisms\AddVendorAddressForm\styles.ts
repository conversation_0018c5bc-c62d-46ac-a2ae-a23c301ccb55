import { styled } from "@/utils/styled";
import { View, Text, Pressable } from "react-native";

export const Container = styled(View)`
  flex: 1;
`;

export const Form = styled(View)`
  gap: 16px;
`;


export const ButtonRow = styled(View)`
  flex-direction: row;
  gap: 12px;
  align-items: center;
  margin-top: 20px;
`;

export const BackButton = styled(Pressable)`
  padding: 12px 24px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  background-color: transparent;
`;

export const BackButtonText = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
`;
