import { styled } from "@/utils/styled";

export const Title = styled.Text`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
`;

export const BackButton = styled.Pressable`
  padding: 8px;
  margin-right: 8px;
`;

export const ActionButton = styled.Pressable`
  padding: 8px;
  margin-left: 8px;
`;

export const CartBadge = styled.View`
  position: absolute;
  top: 0;
  right: 0;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  justify-content: center;
  align-items: center;
  padding-horizontal: 4px;
  background-color: ${({ theme }) => theme.colors.primary};
`;

export const CartBadgeText = styled.Text`
  color: ${({ theme }) => theme.colors.white};
  font-size: 12px;
  font-weight: bold;
`;

export const TitleContainer = styled.View`
  flex: 1;
  align-items: center;
`;

export const HeaderContainer = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background};
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.lightGray};
`;

export const SideContainer = styled.View`
  width: 50px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
`;
