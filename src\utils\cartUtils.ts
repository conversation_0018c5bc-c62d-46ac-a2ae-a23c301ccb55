/**
 * Cart utility functions for calculations and formatting
 */

interface CartItem {
  id: number;
  product: {
    id: number;
    title: string;
  };
  quantity: number;
  price: string;
}

interface CartCalculations {
  subtotal: number;
  cgstAmount: number;
  sgstAmount: number;
  igstAmount: number;
  totalAmount: number;
  itemCount: number;
}

/**
 * Calculate cart totals and taxes
 */
export const calculateCartTotals = (
  items: CartItem[],
  cgstPercentage: number = 0,
  sgstPercentage: number = 0,
  igstPercentage: number = 0
): CartCalculations => {
  const subtotal = items.reduce((total, item) => {
    return total + (item.quantity * parseFloat(item.price));
  }, 0);

  const cgstAmount = (subtotal * cgstPercentage) / 100;
  const sgstAmount = (subtotal * sgstPercentage) / 100;
  const igstAmount = (subtotal * igstPercentage) / 100;
  const totalAmount = subtotal + cgstAmount + sgstAmount + igstAmount;
  const itemCount = items.reduce((count, item) => count + item.quantity, 0);

  return {
    subtotal,
    cgstAmount,
    sgstAmount,
    igstAmount,
    totalAmount,
    itemCount,
  };
};

/**
 * Format price with currency symbol
 */
export const formatPrice = (amount: string | number, currency: string = '₹'): string => {
  const numValue = typeof amount === 'string' ? parseFloat(amount) : amount;
  return `${currency}${isNaN(numValue) ? '0.00' : numValue.toFixed(2)}`;
};

/**
 * Format percentage display
 */
export const formatPercentage = (percentage: string | number): string => {
  const numValue = typeof percentage === 'string' ? parseFloat(percentage) : percentage;
  return `${isNaN(numValue) ? '0' : numValue.toFixed(1)}%`;
};

/**
 * Check if cart is empty
 */
export const isCartEmpty = (items: CartItem[]): boolean => {
  return !items || items.length === 0;
};

/**
 * Get cart item count
 */
export const getCartItemCount = (items: CartItem[]): number => {
  return items.reduce((count, item) => count + item.quantity, 0);
};

/**
 * Find cart item by product ID
 */
export const findCartItemByProductId = (items: CartItem[], productId: number): CartItem | undefined => {
  return items.find(item => item.product.id === productId);
};

/**
 * Validate cart item quantity
 */
export const validateQuantity = (quantity: number, minQuantity: number = 1, maxQuantity: number = 999): boolean => {
  return quantity >= minQuantity && quantity <= maxQuantity;
};

/**
 * Calculate item total price
 */
export const calculateItemTotal = (quantity: number, price: string | number): number => {
  const numPrice = typeof price === 'string' ? parseFloat(price) : price;
  return quantity * (isNaN(numPrice) ? 0 : numPrice);
};

/**
 * Generate cart summary text
 */
export const generateCartSummary = (items: CartItem[]): string => {
  const itemCount = getCartItemCount(items);
  const uniqueItems = items.length;
  
  if (itemCount === 0) return 'Empty cart';
  if (itemCount === 1) return '1 item';
  if (uniqueItems === 1) return `${itemCount} items`;
  
  return `${itemCount} items (${uniqueItems} products)`;
};

/**
 * Check if two cart items are the same product
 */
export const isSameProduct = (item1: CartItem, item2: CartItem): boolean => {
  return item1.product.id === item2.product.id;
};

/**
 * Sort cart items by name
 */
export const sortCartItemsByName = (items: CartItem[]): CartItem[] => {
  return [...items].sort((a, b) => 
    a.product.title.localeCompare(b.product.title)
  );
};

/**
 * Sort cart items by price (high to low)
 */
export const sortCartItemsByPrice = (items: CartItem[], ascending: boolean = false): CartItem[] => {
  return [...items].sort((a, b) => {
    const priceA = parseFloat(a.price);
    const priceB = parseFloat(b.price);
    return ascending ? priceA - priceB : priceB - priceA;
  });
};
