import React from "react";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import { OrderStatusEnum } from "@/types/order";
import { SectionHeader, ProformaInvoiceButton } from "@/components/atoms";
import { OrderInfoCard } from "@/components/molecules";

interface OrderHeaderProps {
  orderId: number;
  orderNumber: string;
  orderDate: string;
  status: OrderStatusEnum;
  statusLabel: string;
  totalAmount: string;
  paymentStatus?: string;
  orderType?: string;
}

/**
 * OrderHeader - Organism component for order header section
 * Displays order overview with status progress and basic information
 */
const OrderHeader: React.FC<OrderHeaderProps> = ({
  orderId,
  orderNumber,
  orderDate,
  status,
  statusLabel,
  totalAmount,
  paymentStatus,
  orderType,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  return (
    <>
      <SectionHeader
        title={t("order.details")}
        icon={
          <Ionicons
            name="receipt-outline"
            size={20}
            color={theme.colors.primary}
          />
        }
      />

      <OrderInfoCard
        orderNumber={orderNumber}
        orderDate={orderDate}
        status={status}
        statusLabel={statusLabel}
        totalAmount={totalAmount}
        paymentStatus={paymentStatus}
        orderType={orderType}
      />

      <ProformaInvoiceButton
        orderId={orderId}
        variant="outline"
        size="medium"
      />
    </>
  );
};

export default OrderHeader;
