import React from "react";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import { SectionHeader } from "@/components/atoms";
import { PricingBreakdown } from "@/components/molecules";
import { Card } from "@/components/molecules";

interface OrderFinancialsProps {
  subtotal: string;
  cgstPercentage?: string;
  sgstPercentage?: string;
  igstPercentage?: string;
  cgstTotal?: string;
  sgstTotal?: string;
  igstTotal?: string;
  discountAmount?: string;
  totalAmount: string;
}

/**
 * OrderFinancials - Organism component for order financial details
 * Displays pricing breakdown with taxes and totals
 */
const OrderFinancials: React.FC<OrderFinancialsProps> = ({
  subtotal,
  cgstPercentage,
  sgstPercentage,
  igstPercentage,
  cgstTotal,
  sgstTotal,
  igstTotal,
  discountAmount,
  totalAmount,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  return (
    <Card variant="elevated">
      <SectionHeader
        title={t("order.pricing_details")}
        icon={
          <Ionicons
            name="calculator-outline"
            size={20}
            color={theme.colors.primary}
          />
        }
      />

      <PricingBreakdown
        subtotal={subtotal}
        cgstPercentage={cgstPercentage}
        sgstPercentage={sgstPercentage}
        igstPercentage={igstPercentage}
        cgstTotal={cgstTotal}
        sgstTotal={sgstTotal}
        igstTotal={igstTotal}
        discountAmount={discountAmount}
        totalAmount={totalAmount}
      />
    </Card>
  );
};

export default OrderFinancials;
